import { Request, Response, NextFunction } from 'express';
import { Valida<PERSON><PERSON>hain } from 'express-validator';
export declare const handleValidationErrors: (req: Request, res: Response, next: NextFunction) => void;
export declare const validate: (validations: ValidationChain[]) => (((req: Request, res: Response, next: NextFunction) => void) | ValidationChain)[];
export declare const validateRequest: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=validation.d.ts.map