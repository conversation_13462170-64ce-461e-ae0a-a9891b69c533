{"version": 3, "file": "response.d.ts", "sourceRoot": "", "sources": ["../../src/utils/response.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAGnC,cAAM,aAAa;IAIjB,MAAM,CAAC,OAAO,CAAC,CAAC,EACd,GAAG,EAAE,QAAQ,EACb,IAAI,CAAC,EAAE,CAAC,EACR,OAAO,GAAE,MAAkB,EAC3B,UAAU,GAAE,MAAY,GACvB,QAAQ;IAcX,MAAM,CAAC,KAAK,CACV,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE,MAAgC,EACzC,UAAU,GAAE,MAAY,EACxB,KAAK,CAAC,EAAE,MAAM,GACb,QAAQ;IAcX,MAAM,CAAC,SAAS,CAAC,CAAC,EAChB,GAAG,EAAE,QAAQ,EACb,IAAI,EAAE,CAAC,EAAE,EACT,WAAW,EAAE,MAAM,EACnB,UAAU,EAAE,MAAM,EAClB,YAAY,EAAE,MAAM,EACpB,OAAO,GAAE,MAAkB,GAC1B,QAAQ;IA4BX,MAAM,CAAC,OAAO,CAAC,CAAC,EACd,GAAG,EAAE,QAAQ,EACb,IAAI,CAAC,EAAE,CAAC,EACR,OAAO,GAAE,MAAwC,GAChD,QAAQ;IAOX,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,GAAG,QAAQ;IAOzC,MAAM,CAAC,UAAU,CACf,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE,MAAsB,EAC/B,KAAK,CAAC,EAAE,MAAM,GACb,QAAQ;IAOX,MAAM,CAAC,YAAY,CACjB,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE,MAAuB,GAC/B,QAAQ;IAOX,MAAM,CAAC,SAAS,CACd,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE,MAAoB,GAC5B,QAAQ;IAOX,MAAM,CAAC,QAAQ,CACb,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE,MAA6B,GACrC,QAAQ;IAOX,MAAM,CAAC,QAAQ,CACb,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE,MAAmB,EAC5B,KAAK,CAAC,EAAE,MAAM,GACb,QAAQ;IAOX,MAAM,CAAC,eAAe,CACpB,GAAG,EAAE,QAAQ,EACb,MAAM,EAAE,GAAG,EACX,OAAO,GAAE,MAA4B,GACpC,QAAQ;CAGZ;AAED,eAAe,aAAa,CAAC"}