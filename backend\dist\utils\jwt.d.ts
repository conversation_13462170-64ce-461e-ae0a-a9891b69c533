import { IUser } from '@/types';
interface TokenPayload {
    userId: string;
    email: string;
    role: string;
}
declare class JWTUtils {
    static generateAccessToken(user: IUser): string;
    static generateRefreshToken(userId: string): Promise<string>;
    static verifyAccessToken(token: string): TokenPayload;
    static verifyRefreshToken(token: string): Promise<string>;
    static revokeRefreshToken(token: string): Promise<void>;
    static revokeAllUserTokens(userId: string): Promise<void>;
    static cleanupExpiredTokens(): Promise<void>;
    static generateTokenPair(user: IUser): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
}
export default JWTUtils;
//# sourceMappingURL=jwt.d.ts.map