import mongoose, { Schema } from 'mongoose';
import { IResponsibility, IResponsibilityItem } from '@/types';

const responsibilityItemSchema = new Schema<IResponsibilityItem>({
  description: {
    type: String,
    required: [true, 'Responsibility description is required'],
    trim: true,
    maxlength: [500, 'Responsibility description cannot exceed 500 characters']
  },
  weight: {
    type: Number,
    required: [true, 'Weight is required'],
    min: [0, 'Weight must be positive'],
    max: [100, 'Weight cannot exceed 100%']
  }
}, { _id: true });

const responsibilitySchema = new Schema<IResponsibility>({
  responsibilityCode: {
    type: String,
    required: [true, 'Responsibility code is required'],
    unique: true,
    trim: true,
    match: [/^TN\d{3}$/, 'Responsibility code must follow format TN001, TN002, etc.']
  },
  employeeName: {
    type: String,
    required: [true, 'Employee name is required'],
    trim: true,
    maxlength: [100, 'Employee name cannot exceed 100 characters']
  },
  currentlyDisplayed: {
    type: Boolean,
    required: [true, 'Currently displayed status is required'],
    default: false
  },
  position: {
    type: String,
    required: [true, 'Position is required'],
    trim: true,
    maxlength: [100, 'Position cannot exceed 100 characters']
  },
  responsibilities: {
    type: [responsibilityItemSchema],
    required: [true, 'At least one responsibility is required'],
    validate: {
      validator: function(responsibilities: IResponsibilityItem[]) {
        return responsibilities.length > 0;
      },
      message: 'At least one responsibility item is required'
    }
  }
}, {
  timestamps: true
});

// Indexes
responsibilitySchema.index({ responsibilityCode: 1 });
responsibilitySchema.index({ employeeName: 1 });
responsibilitySchema.index({ currentlyDisplayed: 1 });
responsibilitySchema.index({ position: 1 });
responsibilitySchema.index({ createdAt: -1 });

// Static method to generate next responsibility code
responsibilitySchema.statics.generateNextCode = async function(): Promise<string> {
  const lastResponsibility = await this.findOne({}, {}, { sort: { responsibilityCode: -1 } });
  
  if (!lastResponsibility) {
    return 'TN001';
  }
  
  const lastCodeNumber = parseInt(lastResponsibility.responsibilityCode.substring(2));
  const nextNumber = lastCodeNumber + 1;
  
  return `TN${nextNumber.toString().padStart(3, '0')}`;
};

// Validation for total weight
responsibilitySchema.pre('save', function(next) {
  const totalWeight = this.responsibilities.reduce((sum, item) => sum + item.weight, 0);
  
  if (totalWeight > 100) {
    return next(new Error('Total weight of responsibilities cannot exceed 100%'));
  }
  
  next();
});

const Responsibility = mongoose.model<IResponsibility>('Responsibility', responsibilitySchema);

export default Responsibility;
