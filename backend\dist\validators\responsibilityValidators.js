"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.responsibilityQueryValidation = exports.getResponsibilityByCodeValidation = exports.getResponsibilityValidation = exports.updateResponsibilityValidation = exports.createResponsibilityValidation = void 0;
const express_validator_1 = require("express-validator");
exports.createResponsibilityValidation = [
    (0, express_validator_1.body)('employeeName')
        .notEmpty()
        .withMessage('Employee name is required')
        .isLength({ min: 1, max: 100 })
        .withMessage('Employee name must be between 1 and 100 characters')
        .trim(),
    (0, express_validator_1.body)('currentlyDisplayed')
        .isBoolean()
        .withMessage('Currently displayed must be a boolean value'),
    (0, express_validator_1.body)('position')
        .notEmpty()
        .withMessage('Position is required')
        .isLength({ min: 1, max: 100 })
        .withMessage('Position must be between 1 and 100 characters')
        .trim(),
    (0, express_validator_1.body)('responsibilities')
        .isArray({ min: 1 })
        .withMessage('At least one responsibility is required'),
    (0, express_validator_1.body)('responsibilities.*.description')
        .notEmpty()
        .withMessage('Responsibility description is required')
        .isLength({ min: 1, max: 500 })
        .withMessage('Responsibility description must be between 1 and 500 characters')
        .trim(),
    (0, express_validator_1.body)('responsibilities.*.weight')
        .isNumeric()
        .withMessage('Weight must be a number')
        .isFloat({ min: 0, max: 100 })
        .withMessage('Weight must be between 0 and 100'),
    (0, express_validator_1.body)('responsibilities').custom((responsibilities) => {
        const totalWeight = responsibilities.reduce((sum, item) => sum + parseFloat(item.weight), 0);
        if (totalWeight > 100) {
            throw new Error('Total weight of responsibilities cannot exceed 100%');
        }
        return true;
    })
];
exports.updateResponsibilityValidation = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('Invalid responsibility ID'),
    (0, express_validator_1.body)('employeeName')
        .optional()
        .isLength({ min: 1, max: 100 })
        .withMessage('Employee name must be between 1 and 100 characters')
        .trim(),
    (0, express_validator_1.body)('currentlyDisplayed')
        .optional()
        .isBoolean()
        .withMessage('Currently displayed must be a boolean value'),
    (0, express_validator_1.body)('position')
        .optional()
        .isLength({ min: 1, max: 100 })
        .withMessage('Position must be between 1 and 100 characters')
        .trim(),
    (0, express_validator_1.body)('responsibilities')
        .optional()
        .isArray({ min: 1 })
        .withMessage('At least one responsibility is required'),
    (0, express_validator_1.body)('responsibilities.*.description')
        .optional()
        .isLength({ min: 1, max: 500 })
        .withMessage('Responsibility description must be between 1 and 500 characters')
        .trim(),
    (0, express_validator_1.body)('responsibilities.*.weight')
        .optional()
        .isNumeric()
        .withMessage('Weight must be a number')
        .isFloat({ min: 0, max: 100 })
        .withMessage('Weight must be between 0 and 100'),
    (0, express_validator_1.body)('responsibilities').optional().custom((responsibilities) => {
        if (responsibilities) {
            const totalWeight = responsibilities.reduce((sum, item) => sum + parseFloat(item.weight), 0);
            if (totalWeight > 100) {
                throw new Error('Total weight of responsibilities cannot exceed 100%');
            }
        }
        return true;
    })
];
exports.getResponsibilityValidation = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('Invalid responsibility ID')
];
exports.getResponsibilityByCodeValidation = [
    (0, express_validator_1.param)('code')
        .matches(/^TN\d{3}$/)
        .withMessage('Responsibility code must follow format TN001, TN002, etc.')
];
exports.responsibilityQueryValidation = [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    (0, express_validator_1.query)('employeeName')
        .optional()
        .isLength({ min: 1, max: 100 })
        .withMessage('Employee name filter must be between 1 and 100 characters')
        .trim(),
    (0, express_validator_1.query)('position')
        .optional()
        .isLength({ min: 1, max: 100 })
        .withMessage('Position filter must be between 1 and 100 characters')
        .trim(),
    (0, express_validator_1.query)('currentlyDisplayed')
        .optional()
        .isIn(['true', 'false'])
        .withMessage('Currently displayed filter must be true or false')
];
//# sourceMappingURL=responsibilityValidators.js.map