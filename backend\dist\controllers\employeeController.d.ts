import { Response } from 'express';
import { AuthenticatedRequest } from '@/types';
declare class EmployeeController {
    static getAllEmployees(req: AuthenticatedRequest, res: Response): Promise<void>;
    static getEmployeeById(req: AuthenticatedRequest, res: Response): Promise<void>;
    static getEmployeeByCode(req: AuthenticatedRequest, res: Response): Promise<void>;
    static createEmployee(req: AuthenticatedRequest, res: Response): Promise<void>;
    static updateEmployee(req: AuthenticatedRequest, res: Response): Promise<void>;
    static deleteEmployee(req: AuthenticatedRequest, res: Response): Promise<void>;
    static resetEmployeePassword(req: AuthenticatedRequest, res: Response): Promise<void>;
    static getEmployeeStats(req: AuthenticatedRequest, res: Response): Promise<void>;
}
export default EmployeeController;
//# sourceMappingURL=employeeController.d.ts.map