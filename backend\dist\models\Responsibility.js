"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const responsibilityItemSchema = new mongoose_1.Schema({
    description: {
        type: String,
        required: [true, 'Responsibility description is required'],
        trim: true,
        maxlength: [500, 'Responsibility description cannot exceed 500 characters']
    },
    weight: {
        type: Number,
        required: [true, 'Weight is required'],
        min: [0, 'Weight must be positive'],
        max: [100, 'Weight cannot exceed 100%']
    }
}, { _id: true });
const responsibilitySchema = new mongoose_1.Schema({
    responsibilityCode: {
        type: String,
        required: [true, 'Responsibility code is required'],
        unique: true,
        trim: true,
        match: [/^TN\d{3}$/, 'Responsibility code must follow format TN001, TN002, etc.']
    },
    employeeName: {
        type: String,
        required: [true, 'Employee name is required'],
        trim: true,
        maxlength: [100, 'Employee name cannot exceed 100 characters']
    },
    currentlyDisplayed: {
        type: Boolean,
        required: [true, 'Currently displayed status is required'],
        default: false
    },
    position: {
        type: String,
        required: [true, 'Position is required'],
        trim: true,
        maxlength: [100, 'Position cannot exceed 100 characters']
    },
    responsibilities: {
        type: [responsibilityItemSchema],
        required: [true, 'At least one responsibility is required'],
        validate: {
            validator: function (responsibilities) {
                return responsibilities.length > 0;
            },
            message: 'At least one responsibility item is required'
        }
    }
}, {
    timestamps: true
});
responsibilitySchema.index({ responsibilityCode: 1 });
responsibilitySchema.index({ employeeName: 1 });
responsibilitySchema.index({ currentlyDisplayed: 1 });
responsibilitySchema.index({ position: 1 });
responsibilitySchema.index({ createdAt: -1 });
responsibilitySchema.statics.generateNextCode = async function () {
    const lastResponsibility = await this.findOne({}, {}, { sort: { responsibilityCode: -1 } });
    if (!lastResponsibility) {
        return 'TN001';
    }
    const lastCodeNumber = parseInt(lastResponsibility.responsibilityCode.substring(2));
    const nextNumber = lastCodeNumber + 1;
    return `TN${nextNumber.toString().padStart(3, '0')}`;
};
responsibilitySchema.pre('save', function (next) {
    const totalWeight = this.responsibilities.reduce((sum, item) => sum + item.weight, 0);
    if (totalWeight > 100) {
        return next(new Error('Total weight of responsibilities cannot exceed 100%'));
    }
    next();
});
const Responsibility = mongoose_1.default.model('Responsibility', responsibilitySchema);
exports.default = Responsibility;
//# sourceMappingURL=Responsibility.js.map