import { config } from '@/config/config';
import Database from '@/config/database';
import App from './app';
import { JWTUtils } from '@/utils';

class Server {
  private app: App;
  private database: Database;

  constructor() {
    this.app = new App();
    this.database = Database.getInstance();
  }

  public async start(): Promise<void> {
    try {
      // Connect to database
      await this.database.connect();

      // Start cleanup job for expired tokens
      this.startTokenCleanupJob();

      // Start server
      const server = this.app.getApp().listen(config.server.port, () => {
        console.log(`
🚀 Server is running!
📍 Environment: ${config.server.nodeEnv}
🌐 URL: http://localhost:${config.server.port}
📊 API: http://localhost:${config.server.port}/api/${config.server.apiVersion}
🏥 Health: http://localhost:${config.server.port}/api/${config.server.apiVersion}/health
📚 Database: ${config.database.name}
        `);
      });

      // Graceful shutdown
      this.setupGracefulShutdown(server);

    } catch (error) {
      console.error('❌ Failed to start server:', error);
      process.exit(1);
    }
  }

  private startTokenCleanupJob(): void {
    // Clean up expired tokens every hour
    setInterval(async () => {
      try {
        await JWTUtils.cleanupExpiredTokens();
        console.log('🧹 Cleaned up expired tokens');
      } catch (error) {
        console.error('❌ Token cleanup failed:', error);
      }
    }, 60 * 60 * 1000); // 1 hour
  }

  private setupGracefulShutdown(server: any): void {
    const gracefulShutdown = async (signal: string) => {
      console.log(`\n📡 Received ${signal}. Starting graceful shutdown...`);

      // Stop accepting new connections
      server.close(async () => {
        console.log('🔌 HTTP server closed');

        try {
          // Close database connection
          await this.database.disconnect();
          console.log('💾 Database connection closed');

          console.log('✅ Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          console.error('❌ Error during shutdown:', error);
          process.exit(1);
        }
      });

      // Force close after 30 seconds
      setTimeout(() => {
        console.error('⏰ Forced shutdown after timeout');
        process.exit(1);
      }, 30000);
    };

    // Listen for termination signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('💥 Uncaught Exception:', error);
      gracefulShutdown('uncaughtException');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });
  }
}

// Start the server
const server = new Server();
server.start();
