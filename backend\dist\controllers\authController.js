"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const models_1 = require("@/models");
const utils_1 = require("@/utils");
class AuthController {
    static async login(req, res) {
        try {
            const { email, password } = req.body;
            const user = await models_1.User.findOne({ email }).select('+password');
            if (!user) {
                utils_1.ResponseUtils.unauthorized(res, 'Invalid email or password');
                return;
            }
            if (!user.isActive) {
                utils_1.ResponseUtils.unauthorized(res, 'Account is deactivated');
                return;
            }
            const isPasswordValid = await user.comparePassword(password);
            if (!isPasswordValid) {
                utils_1.ResponseUtils.unauthorized(res, 'Invalid email or password');
                return;
            }
            user.lastLogin = new Date();
            await user.save();
            const { accessToken, refreshToken } = await utils_1.JWTUtils.generateTokenPair(user);
            const userResponse = user.toJSON();
            utils_1.ResponseUtils.success(res, {
                user: userResponse,
                accessToken,
                refreshToken
            }, 'Login successful');
        }
        catch (error) {
            console.error('Login error:', error);
            utils_1.ResponseUtils.error(res, 'Login failed');
        }
    }
    static async refreshToken(req, res) {
        try {
            const { refreshToken } = req.body;
            const userId = await utils_1.JWTUtils.verifyRefreshToken(refreshToken);
            const user = await models_1.User.findById(userId);
            if (!user || !user.isActive) {
                utils_1.ResponseUtils.unauthorized(res, 'User not found or inactive');
                return;
            }
            await utils_1.JWTUtils.revokeRefreshToken(refreshToken);
            const { accessToken, refreshToken: newRefreshToken } = await utils_1.JWTUtils.generateTokenPair(user);
            utils_1.ResponseUtils.success(res, {
                accessToken,
                refreshToken: newRefreshToken
            }, 'Token refreshed successfully');
        }
        catch (error) {
            console.error('Token refresh error:', error);
            utils_1.ResponseUtils.unauthorized(res, 'Invalid or expired refresh token');
        }
    }
    static async logout(req, res) {
        try {
            const { refreshToken } = req.body;
            if (refreshToken) {
                await utils_1.JWTUtils.revokeRefreshToken(refreshToken);
            }
            utils_1.ResponseUtils.success(res, null, 'Logout successful');
        }
        catch (error) {
            console.error('Logout error:', error);
            utils_1.ResponseUtils.error(res, 'Logout failed');
        }
    }
    static async logoutAll(req, res) {
        try {
            if (!req.user) {
                utils_1.ResponseUtils.unauthorized(res, 'User not authenticated');
                return;
            }
            await utils_1.JWTUtils.revokeAllUserTokens(req.user._id);
            utils_1.ResponseUtils.success(res, null, 'Logged out from all devices');
        }
        catch (error) {
            console.error('Logout all error:', error);
            utils_1.ResponseUtils.error(res, 'Logout failed');
        }
    }
    static async getProfile(req, res) {
        try {
            if (!req.user) {
                utils_1.ResponseUtils.unauthorized(res, 'User not authenticated');
                return;
            }
            utils_1.ResponseUtils.success(res, req.user, 'Profile retrieved successfully');
        }
        catch (error) {
            console.error('Get profile error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to retrieve profile');
        }
    }
    static async updateProfile(req, res) {
        try {
            if (!req.user) {
                utils_1.ResponseUtils.unauthorized(res, 'User not authenticated');
                return;
            }
            const allowedUpdates = ['firstName', 'lastName', 'department', 'position'];
            const updates = Object.keys(req.body)
                .filter(key => allowedUpdates.includes(key))
                .reduce((obj, key) => {
                obj[key] = req.body[key];
                return obj;
            }, {});
            const updatedUser = await models_1.User.findByIdAndUpdate(req.user._id, updates, { new: true, runValidators: true });
            utils_1.ResponseUtils.success(res, updatedUser, 'Profile updated successfully');
        }
        catch (error) {
            console.error('Update profile error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to update profile');
        }
    }
    static async changePassword(req, res) {
        try {
            if (!req.user) {
                utils_1.ResponseUtils.unauthorized(res, 'User not authenticated');
                return;
            }
            const { currentPassword, newPassword } = req.body;
            const user = await models_1.User.findById(req.user._id).select('+password');
            if (!user) {
                utils_1.ResponseUtils.notFound(res, 'User not found');
                return;
            }
            const isCurrentPasswordValid = await user.comparePassword(currentPassword);
            if (!isCurrentPasswordValid) {
                utils_1.ResponseUtils.badRequest(res, 'Current password is incorrect');
                return;
            }
            user.password = newPassword;
            await user.save();
            await utils_1.JWTUtils.revokeAllUserTokens(user._id);
            utils_1.ResponseUtils.success(res, null, 'Password changed successfully');
        }
        catch (error) {
            console.error('Change password error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to change password');
        }
    }
}
exports.default = AuthController;
//# sourceMappingURL=authController.js.map