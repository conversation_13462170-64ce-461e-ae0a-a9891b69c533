# ERP Frontend - Authentication System

## 🎯 Tính năng đã triển khai

### ✅ Trang đăng nhập (Login)
- **URL**: `/login`
- **Tính năng**:
  - Form đăng nhập với email và mật khẩu
  - Validation form phía client
  - Hiển thị/ẩn mật khẩu
  - Xử lý lỗi từ API
  - Loading state khi đăng nhập
  - Responsive design
  - Link đến trang đăng ký và quên mật khẩu

### ✅ Trang đăng ký (Register)
- **URL**: `/register`
- **Tính năng**:
  - Form đăng ký với đầy đủ thông tin
  - Validation mật khẩu mạnh
  - Xác nhận mật khẩu
  - Validation email và username
  - Xử lý lỗi từ API
  - Loading state khi đăng ký
  - Link quay lại đăng nhập

### ✅ Trang quên mật khẩu (Forgot Password)
- **URL**: `/forgot-password`
- **Tính năng**:
  - Form nhập email
  - Trang xác nhận đã gửi email
  - Link quay lại đăng nhập
  - UI/UX thân thiện

### ✅ Authentication Context
- **Tính năng**:
  - Quản lý trạng thái đăng nhập toàn cục
  - Lưu trữ thông tin user và tokens
  - Auto-refresh token
  - Logout từ tất cả thiết bị

### ✅ Protected Routes
- **Tính năng**:
  - Bảo vệ các trang cần đăng nhập
  - Redirect về login nếu chưa đăng nhập
  - Loading state khi kiểm tra authentication

### ✅ Layout với Header
- **Tính năng**:
  - Hiển thị thông tin user đã đăng nhập
  - Nút đăng xuất
  - Responsive header

## 🎨 UI/UX Design

### Thiết kế theo yêu cầu:
- ✅ Logo và branding ABF System
- ✅ Màu sắc chủ đạo: Blue (#2563eb)
- ✅ Layout 2 cột (form + background)
- ✅ Icons cho các input fields
- ✅ Responsive design
- ✅ Loading states và error handling
- ✅ Typography và spacing nhất quán

## 🔧 Technical Stack

### Frontend:
- **React 18** với TypeScript
- **React Router** cho routing
- **Tailwind CSS** cho styling
- **Lucide React** cho icons
- **Axios** cho HTTP requests

### State Management:
- **React Context** cho authentication
- **Local Storage** cho token persistence

## 📁 Cấu trúc Files

```
src/
├── components/
│   ├── Button.tsx           # Reusable button component
│   ├── Input.tsx            # Reusable input component
│   ├── Layout.tsx           # Main layout with header
│   ├── ProtectedRoute.tsx   # Route protection
│   └── ProtectedLayout.tsx  # Layout wrapper for protected routes
├── contexts/
│   └── AuthContext.tsx      # Authentication context
├── pages/
│   ├── Login.tsx           # Login page
│   ├── Register.tsx        # Register page
│   └── ForgotPassword.tsx  # Forgot password page
├── services/
│   └── authService.ts      # API service for authentication
├── types/
│   ├── auth.ts            # Authentication types
│   └── index.ts           # Type exports
└── App.tsx                # Main app with routing
```

## 🚀 Cách sử dụng

### 1. Chạy Backend:
```bash
cd backend
npm run dev
```

### 2. Chạy Frontend:
```bash
cd erp
npm run dev
```

### 3. Truy cập:
- **Frontend**: http://localhost:5174
- **Backend API**: http://localhost:5000

### 4. Test Authentication:
1. Mở http://localhost:5174 (sẽ redirect về /login)
2. Đăng ký tài khoản mới tại `/register`
3. Hoặc đăng nhập với tài khoản đã có
4. Sau khi đăng nhập thành công, sẽ redirect về `/dashboard`

## 🔐 API Endpoints

### Authentication:
- `POST /api/v1/auth/register` - Đăng ký
- `POST /api/v1/auth/login` - Đăng nhập
- `POST /api/v1/auth/logout` - Đăng xuất
- `POST /api/v1/auth/refresh-token` - Refresh token

## 🎯 Validation Rules

### Đăng ký:
- **Username**: Tối thiểu 3 ký tự
- **Email**: Format email hợp lệ
- **Password**: Tối thiểu 6 ký tự, có chữ hoa, chữ thường, số
- **Confirm Password**: Phải khớp với password

### Đăng nhập:
- **Email**: Bắt buộc và format hợp lệ
- **Password**: Bắt buộc

## 🔄 Flow Authentication

1. **User chưa đăng nhập**: Redirect về `/login`
2. **Đăng nhập thành công**: Lưu tokens, redirect về `/dashboard`
3. **Token hết hạn**: Auto refresh hoặc redirect về `/login`
4. **Đăng xuất**: Clear tokens, redirect về `/login`

## 🎨 Screenshots

### Trang đăng nhập:
- Form đăng nhập với email/password
- Background gradient với thông tin hệ thống
- Links đến đăng ký và quên mật khẩu

### Trang đăng ký:
- Form đăng ký đầy đủ thông tin
- Validation real-time
- Confirm password

### Trang quên mật khẩu:
- Form nhập email
- Trang xác nhận đã gửi email

## ✨ Tính năng nổi bật

1. **Responsive Design**: Hoạt động tốt trên mọi thiết bị
2. **Real-time Validation**: Kiểm tra lỗi ngay khi user nhập
3. **Loading States**: UX mượt mà với loading indicators
4. **Error Handling**: Hiển thị lỗi rõ ràng và hữu ích
5. **Auto Token Refresh**: Tự động gia hạn session
6. **Secure Storage**: Lưu trữ tokens an toàn
7. **Clean UI**: Thiết kế đẹp, chuyên nghiệp

## 🔮 Tính năng có thể mở rộng

1. **Two-Factor Authentication (2FA)**
2. **Social Login** (Google, Facebook)
3. **Remember Me** functionality
4. **Password strength meter**
5. **Account verification** via email
6. **Password reset** functionality
7. **Session management** (view active sessions)
8. **Role-based UI** (hiển thị khác nhau theo role)
