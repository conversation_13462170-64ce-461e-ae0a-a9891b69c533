"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authRoutes_1 = __importDefault(require("./authRoutes"));
const employeeRoutes_1 = __importDefault(require("./employeeRoutes"));
const responsibilityRoutes_1 = __importDefault(require("./responsibilityRoutes"));
const router = (0, express_1.Router)();
router.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
    });
});
router.post('/test-cors', (req, res) => {
    console.log('🔍 Test CORS Request:', {
        origin: req.headers.origin,
        method: req.method,
        body: req.body,
        headers: req.headers
    });
    res.json({
        success: true,
        message: 'CORS test successful',
        data: {
            origin: req.headers.origin,
            method: req.method,
            body: req.body
        },
        timestamp: new Date().toISOString()
    });
});
router.use('/auth', authRoutes_1.default);
router.use('/employees', employeeRoutes_1.default);
router.use('/responsibilities', responsibilityRoutes_1.default);
exports.default = router;
//# sourceMappingURL=index.js.map