import { body, param, query } from 'express-validator';

// Validation for creating responsibility
export const createResponsibilityValidation = [
  body('employeeName')
    .notEmpty()
    .withMessage('Employee name is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Employee name must be between 1 and 100 characters')
    .trim(),

  body('currentlyDisplayed')
    .isBoolean()
    .withMessage('Currently displayed must be a boolean value'),

  body('position')
    .notEmpty()
    .withMessage('Position is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Position must be between 1 and 100 characters')
    .trim(),

  body('responsibilities')
    .isArray({ min: 1 })
    .withMessage('At least one responsibility is required'),

  body('responsibilities.*.description')
    .notEmpty()
    .withMessage('Responsibility description is required')
    .isLength({ min: 1, max: 500 })
    .withMessage('Responsibility description must be between 1 and 500 characters')
    .trim(),

  body('responsibilities.*.weight')
    .isNumeric()
    .withMessage('Weight must be a number')
    .isFloat({ min: 0, max: 100 })
    .withMessage('Weight must be between 0 and 100'),

  // Custom validation for total weight
  body('responsibilities').custom((responsibilities) => {
    const totalWeight = responsibilities.reduce((sum: number, item: any) => sum + parseFloat(item.weight), 0);
    if (totalWeight > 100) {
      throw new Error('Total weight of responsibilities cannot exceed 100%');
    }
    return true;
  })
];

// Validation for updating responsibility
export const updateResponsibilityValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid responsibility ID'),

  body('employeeName')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Employee name must be between 1 and 100 characters')
    .trim(),

  body('currentlyDisplayed')
    .optional()
    .isBoolean()
    .withMessage('Currently displayed must be a boolean value'),

  body('position')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Position must be between 1 and 100 characters')
    .trim(),

  body('responsibilities')
    .optional()
    .isArray({ min: 1 })
    .withMessage('At least one responsibility is required'),

  body('responsibilities.*.description')
    .optional()
    .isLength({ min: 1, max: 500 })
    .withMessage('Responsibility description must be between 1 and 500 characters')
    .trim(),

  body('responsibilities.*.weight')
    .optional()
    .isNumeric()
    .withMessage('Weight must be a number')
    .isFloat({ min: 0, max: 100 })
    .withMessage('Weight must be between 0 and 100'),

  // Custom validation for total weight
  body('responsibilities').optional().custom((responsibilities) => {
    if (responsibilities) {
      const totalWeight = responsibilities.reduce((sum: number, item: any) => sum + parseFloat(item.weight), 0);
      if (totalWeight > 100) {
        throw new Error('Total weight of responsibilities cannot exceed 100%');
      }
    }
    return true;
  })
];

// Validation for getting responsibility by ID
export const getResponsibilityValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid responsibility ID')
];

// Validation for getting responsibility by code
export const getResponsibilityByCodeValidation = [
  param('code')
    .matches(/^TN\d{3}$/)
    .withMessage('Responsibility code must follow format TN001, TN002, etc.')
];

// Validation for responsibility query parameters
export const responsibilityQueryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('employeeName')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Employee name filter must be between 1 and 100 characters')
    .trim(),

  query('position')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Position filter must be between 1 and 100 characters')
    .trim(),

  query('currentlyDisplayed')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('Currently displayed filter must be true or false')
];
