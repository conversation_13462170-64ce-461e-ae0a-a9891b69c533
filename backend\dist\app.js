"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const config_1 = require("@/config/config");
const middleware_1 = require("@/middleware");
const routes_1 = __importDefault(require("@/routes"));
class App {
    constructor() {
        this.app = (0, express_1.default)();
        this.initializeMiddlewares();
        this.initializeRoutes();
        this.initializeErrorHandling();
    }
    initializeMiddlewares() {
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
            crossOriginEmbedderPolicy: false,
        }));
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:5173',
            'http://localhost:5174',
            config_1.config.cors.origin
        ];
        this.app.use((0, cors_1.default)({
            origin: (origin, callback) => {
                console.log('🔍 CORS Origin Check:', origin);
                if (!origin) {
                    console.log('✅ CORS: No origin, allowing request');
                    return callback(null, true);
                }
                if (allowedOrigins.includes(origin)) {
                    console.log('✅ CORS: Origin allowed:', origin);
                    return callback(null, true);
                }
                if (config_1.config.server.nodeEnv === 'development') {
                    console.log('✅ CORS: Development mode, allowing all origins');
                    return callback(null, true);
                }
                console.log('❌ CORS: Origin not allowed:', origin);
                const msg = 'The CORS policy for this site does not allow access from the specified Origin.';
                return callback(new Error(msg), false);
            },
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
            preflightContinue: false,
            optionsSuccessStatus: 200
        }));
        const limiter = (0, express_rate_limit_1.default)({
            windowMs: config_1.config.security.rateLimitWindowMs,
            max: config_1.config.security.rateLimitMaxRequests,
            message: {
                error: 'Too many requests from this IP, please try again later.',
            },
            standardHeaders: true,
            legacyHeaders: false,
        });
        this.app.use('/api/', limiter);
        this.app.use((0, compression_1.default)());
        if (config_1.config.server.nodeEnv === 'development') {
            this.app.use((0, morgan_1.default)('dev'));
        }
        else {
            this.app.use((0, morgan_1.default)('combined'));
        }
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        this.app.set('trust proxy', 1);
    }
    initializeRoutes() {
        this.app.use(`/api/${config_1.config.server.apiVersion}`, routes_1.default);
        this.app.get('/', (req, res) => {
            res.json({
                message: 'ERP System API',
                version: config_1.config.server.apiVersion,
                environment: config_1.config.server.nodeEnv,
                timestamp: new Date().toISOString(),
            });
        });
    }
    initializeErrorHandling() {
        this.app.use(middleware_1.notFoundHandler);
        this.app.use(middleware_1.errorHandler);
    }
    getApp() {
        return this.app;
    }
}
exports.default = App;
//# sourceMappingURL=app.js.map