"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Responsibility_1 = __importDefault(require("@/models/Responsibility"));
class ResponsibilityController {
    static async getAllResponsibilities(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const skip = (page - 1) * limit;
            const filter = {};
            if (req.query.employeeName) {
                filter.employeeName = { $regex: req.query.employeeName, $options: 'i' };
            }
            if (req.query.position) {
                filter.position = { $regex: req.query.position, $options: 'i' };
            }
            if (req.query.currentlyDisplayed !== undefined) {
                filter.currentlyDisplayed = req.query.currentlyDisplayed === 'true';
            }
            const [responsibilities, total] = await Promise.all([
                Responsibility_1.default.find(filter)
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limit)
                    .lean(),
                Responsibility_1.default.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limit);
            res.json({
                success: true,
                message: 'Responsibilities retrieved successfully',
                data: {
                    responsibilities,
                    pagination: {
                        currentPage: page,
                        totalPages,
                        totalItems: total,
                        itemsPerPage: limit,
                        hasNextPage: page < totalPages,
                        hasPrevPage: page > 1
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error getting responsibilities:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve responsibilities',
                timestamp: new Date().toISOString()
            });
        }
    }
    static async createResponsibility(req, res) {
        try {
            const { employeeName, currentlyDisplayed, position, responsibilities } = req.body;
            const responsibilityCode = await Responsibility_1.default.generateNextCode();
            const totalWeight = responsibilities.reduce((sum, item) => sum + item.weight, 0);
            if (totalWeight > 100) {
                res.status(400).json({
                    success: false,
                    message: 'Total weight of responsibilities cannot exceed 100%',
                    timestamp: new Date().toISOString()
                });
                return;
            }
            const newResponsibility = new Responsibility_1.default({
                responsibilityCode,
                employeeName,
                currentlyDisplayed,
                position,
                responsibilities
            });
            const savedResponsibility = await newResponsibility.save();
            res.status(201).json({
                success: true,
                message: 'Responsibility created successfully',
                data: savedResponsibility,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error creating responsibility:', error);
            if (error.name === 'ValidationError') {
                const validationErrors = Object.values(error.errors).map((err) => err.message);
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: validationErrors,
                    timestamp: new Date().toISOString()
                });
                return;
            }
            if (error.code === 11000) {
                res.status(400).json({
                    success: false,
                    message: 'Responsibility code already exists',
                    timestamp: new Date().toISOString()
                });
                return;
            }
            res.status(500).json({
                success: false,
                message: 'Failed to create responsibility',
                timestamp: new Date().toISOString()
            });
        }
    }
    static async getResponsibilityById(req, res) {
        try {
            const { id } = req.params;
            const responsibility = await Responsibility_1.default.findById(id).lean();
            if (!responsibility) {
                res.status(404).json({
                    success: false,
                    message: 'Responsibility not found',
                    timestamp: new Date().toISOString()
                });
                return;
            }
            res.json({
                success: true,
                message: 'Responsibility retrieved successfully',
                data: responsibility,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error getting responsibility:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve responsibility',
                timestamp: new Date().toISOString()
            });
        }
    }
    static async getResponsibilityByCode(req, res) {
        try {
            const { code } = req.params;
            const responsibility = await Responsibility_1.default.findOne({ responsibilityCode: code }).lean();
            if (!responsibility) {
                res.status(404).json({
                    success: false,
                    message: 'Responsibility not found',
                    timestamp: new Date().toISOString()
                });
                return;
            }
            res.json({
                success: true,
                message: 'Responsibility retrieved successfully',
                data: responsibility,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error getting responsibility by code:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve responsibility',
                timestamp: new Date().toISOString()
            });
        }
    }
    static async updateResponsibility(req, res) {
        try {
            const { id } = req.params;
            const { employeeName, currentlyDisplayed, position, responsibilities } = req.body;
            if (responsibilities) {
                const totalWeight = responsibilities.reduce((sum, item) => sum + item.weight, 0);
                if (totalWeight > 100) {
                    res.status(400).json({
                        success: false,
                        message: 'Total weight of responsibilities cannot exceed 100%',
                        timestamp: new Date().toISOString()
                    });
                    return;
                }
            }
            const updatedResponsibility = await Responsibility_1.default.findByIdAndUpdate(id, {
                employeeName,
                currentlyDisplayed,
                position,
                responsibilities
            }, { new: true, runValidators: true });
            if (!updatedResponsibility) {
                res.status(404).json({
                    success: false,
                    message: 'Responsibility not found',
                    timestamp: new Date().toISOString()
                });
                return;
            }
            res.json({
                success: true,
                message: 'Responsibility updated successfully',
                data: updatedResponsibility,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error updating responsibility:', error);
            if (error.name === 'ValidationError') {
                const validationErrors = Object.values(error.errors).map((err) => err.message);
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: validationErrors,
                    timestamp: new Date().toISOString()
                });
                return;
            }
            res.status(500).json({
                success: false,
                message: 'Failed to update responsibility',
                timestamp: new Date().toISOString()
            });
        }
    }
    static async deleteResponsibility(req, res) {
        try {
            const { id } = req.params;
            const deletedResponsibility = await Responsibility_1.default.findByIdAndDelete(id);
            if (!deletedResponsibility) {
                res.status(404).json({
                    success: false,
                    message: 'Responsibility not found',
                    timestamp: new Date().toISOString()
                });
                return;
            }
            res.json({
                success: true,
                message: 'Responsibility deleted successfully',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error deleting responsibility:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete responsibility',
                timestamp: new Date().toISOString()
            });
        }
    }
    static async getResponsibilityStats(req, res) {
        try {
            const [totalCount, displayedCount, positionStats] = await Promise.all([
                Responsibility_1.default.countDocuments(),
                Responsibility_1.default.countDocuments({ currentlyDisplayed: true }),
                Responsibility_1.default.aggregate([
                    {
                        $group: {
                            _id: '$position',
                            count: { $sum: 1 }
                        }
                    },
                    { $sort: { count: -1 } }
                ])
            ]);
            const stats = {
                totalResponsibilities: totalCount,
                displayedResponsibilities: displayedCount,
                hiddenResponsibilities: totalCount - displayedCount,
                positionBreakdown: positionStats
            };
            res.json({
                success: true,
                message: 'Responsibility statistics retrieved successfully',
                data: stats,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Error getting responsibility stats:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve responsibility statistics',
                timestamp: new Date().toISOString()
            });
        }
    }
}
exports.default = ResponsibilityController;
//# sourceMappingURL=responsibilityController.js.map