# ERP System Backend

A robust backend API for ERP (Enterprise Resource Planning) system built with TypeScript, Express.js, and MongoDB.

## 🚀 Features

- **Authentication & Authorization**: JWT-based authentication with refresh tokens
- **User Management**: User registration, login, profile management
- **Security**: Rate limiting, CORS, Helmet security headers
- **Validation**: Request validation using express-validator
- **Error Handling**: Centralized error handling with custom error types
- **Database**: MongoDB with Mongoose ODM
- **TypeScript**: Full TypeScript support with strict type checking
- **Logging**: Request logging with Morgan
- **Environment Configuration**: Environment-based configuration

## 📋 Prerequisites

- Node.js (v16 or higher)
- MongoDB (v4.4 or higher)
- npm or yarn

## 🛠️ Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd backend
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Update environment variables in `.env`:
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/mydatabase
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key
```

## 🚀 Running the Application

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm run build
npm start
```

## 📁 Project Structure

```
src/
├── config/          # Configuration files
│   ├── config.ts    # Environment configuration
│   └── database.ts  # Database connection
├── controllers/     # Route controllers
│   └── authController.ts
├── middleware/      # Custom middleware
│   ├── auth.ts      # Authentication middleware
│   ├── errorHandler.ts
│   └── validation.ts
├── models/          # Database models
│   ├── User.ts
│   └── RefreshToken.ts
├── routes/          # API routes
│   └── authRoutes.ts
├── types/           # TypeScript type definitions
│   └── index.ts
├── utils/           # Utility functions
│   ├── jwt.ts       # JWT utilities
│   └── response.ts  # Response utilities
├── validators/      # Request validators
│   └── authValidators.ts
├── app.ts           # Express app configuration
└── server.ts        # Server entry point
```

## 🔐 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh-token` - Refresh access token
- `POST /api/v1/auth/logout` - Logout user
- `POST /api/v1/auth/logout-all` - Logout from all devices

### User Profile
- `GET /api/v1/auth/profile` - Get user profile
- `PUT /api/v1/auth/profile` - Update user profile
- `PUT /api/v1/auth/change-password` - Change password

### Health Check
- `GET /api/v1/health` - API health check

## 🔧 Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run clean` - Clean build directory

## 🛡️ Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: Bcrypt with salt rounds
- **Rate Limiting**: Prevent brute force attacks
- **CORS**: Cross-origin resource sharing configuration
- **Helmet**: Security headers
- **Input Validation**: Request validation and sanitization

## 🗄️ Database Schema

### User Model
```typescript
{
  username: string (unique)
  email: string (unique)
  password: string (hashed)
  firstName: string
  lastName: string
  role: enum ['admin', 'manager', 'employee', 'viewer']
  department?: string
  position?: string
  isActive: boolean
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
}
```

### RefreshToken Model
```typescript
{
  token: string (unique)
  userId: string (ref: User)
  expiresAt: Date
  isRevoked: boolean
  createdAt: Date
}
```

## 🚀 Deployment

1. Build the application:
```bash
npm run build
```

2. Set production environment variables

3. Start the server:
```bash
npm start
```

## 📝 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| NODE_ENV | Environment mode | development |
| PORT | Server port | 5000 |
| MONGODB_URI | MongoDB connection string | mongodb://localhost:27017/mydatabase |
| JWT_SECRET | JWT secret key | - |
| JWT_EXPIRE | JWT expiration time | 7d |
| JWT_REFRESH_SECRET | Refresh token secret | - |
| JWT_REFRESH_EXPIRE | Refresh token expiration | 30d |
| BCRYPT_SALT_ROUNDS | Bcrypt salt rounds | 12 |
| CORS_ORIGIN | CORS origin | http://localhost:3000 |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the ISC License.
