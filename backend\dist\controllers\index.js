"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmployeeController = exports.AuthController = void 0;
var authController_1 = require("./authController");
Object.defineProperty(exports, "AuthController", { enumerable: true, get: function () { return __importDefault(authController_1).default; } });
var employeeController_1 = require("./employeeController");
Object.defineProperty(exports, "EmployeeController", { enumerable: true, get: function () { return __importDefault(employeeController_1).default; } });
//# sourceMappingURL=index.js.map