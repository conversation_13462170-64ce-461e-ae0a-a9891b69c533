import { Response } from 'express';
import { ApiResponse, PaginatedResponse } from '@/types';

class ResponseUtils {
  /**
   * Send success response
   */
  static success<T>(
    res: Response,
    data?: T,
    message: string = 'Success',
    statusCode: number = 200
  ): Response {
    const response: ApiResponse<T> = {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    };

    return res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static error(
    res: Response,
    message: string = 'Internal Server Error',
    statusCode: number = 500,
    error?: string
  ): Response {
    const response: ApiResponse = {
      success: false,
      message,
      error,
      timestamp: new Date().toISOString()
    };

    return res.status(statusCode).json(response);
  }

  /**
   * Send paginated response
   */
  static paginated<T>(
    res: Response,
    data: T[],
    currentPage: number,
    totalItems: number,
    itemsPerPage: number,
    message: string = 'Success'
  ): Response {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    
    const paginatedResponse: PaginatedResponse<T> = {
      data,
      pagination: {
        currentPage,
        totalPages,
        totalItems,
        itemsPerPage,
        hasNextPage: currentPage < totalPages,
        hasPrevPage: currentPage > 1
      }
    };

    const response: ApiResponse<PaginatedResponse<T>> = {
      success: true,
      message,
      data: paginatedResponse,
      timestamp: new Date().toISOString()
    };

    return res.status(200).json(response);
  }

  /**
   * Send created response
   */
  static created<T>(
    res: Response,
    data?: T,
    message: string = 'Resource created successfully'
  ): Response {
    return this.success(res, data, message, 201);
  }

  /**
   * Send no content response
   */
  static noContent(res: Response): Response {
    return res.status(204).send();
  }

  /**
   * Send bad request response
   */
  static badRequest(
    res: Response,
    message: string = 'Bad Request',
    error?: string
  ): Response {
    return this.error(res, message, 400, error);
  }

  /**
   * Send unauthorized response
   */
  static unauthorized(
    res: Response,
    message: string = 'Unauthorized'
  ): Response {
    return this.error(res, message, 401);
  }

  /**
   * Send forbidden response
   */
  static forbidden(
    res: Response,
    message: string = 'Forbidden'
  ): Response {
    return this.error(res, message, 403);
  }

  /**
   * Send not found response
   */
  static notFound(
    res: Response,
    message: string = 'Resource not found'
  ): Response {
    return this.error(res, message, 404);
  }

  /**
   * Send conflict response
   */
  static conflict(
    res: Response,
    message: string = 'Conflict',
    error?: string
  ): Response {
    return this.error(res, message, 409, error);
  }

  /**
   * Send validation error response
   */
  static validationError(
    res: Response,
    errors: any,
    message: string = 'Validation failed'
  ): Response {
    return this.error(res, message, 422, JSON.stringify(errors));
  }
}

export default ResponseUtils;
