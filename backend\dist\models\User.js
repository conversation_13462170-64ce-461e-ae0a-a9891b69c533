"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const types_1 = require("@/types");
const userSchema = new mongoose_1.Schema({
    username: {
        type: String,
        required: [true, 'Username is required'],
        unique: true,
        trim: true,
        minlength: [3, 'Username must be at least 3 characters long'],
        maxlength: [30, 'Username cannot exceed 30 characters']
    },
    email: {
        type: String,
        required: [true, 'Email is required'],
        unique: true,
        lowercase: true,
        trim: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    password: {
        type: String,
        required: [true, 'Password is required'],
        minlength: [6, 'Password must be at least 6 characters long'],
        select: false
    },
    firstName: {
        type: String,
        required: [true, 'First name is required'],
        trim: true,
        maxlength: [50, 'First name cannot exceed 50 characters']
    },
    lastName: {
        type: String,
        required: [true, 'Last name is required'],
        trim: true,
        maxlength: [50, 'Last name cannot exceed 50 characters']
    },
    role: {
        type: String,
        enum: Object.values(types_1.UserRole),
        default: types_1.UserRole.EMPLOYEE,
        required: true
    },
    department: {
        type: String,
        trim: true,
        maxlength: [100, 'Department cannot exceed 100 characters']
    },
    position: {
        type: String,
        trim: true,
        maxlength: [100, 'Position cannot exceed 100 characters']
    },
    employeeCode: {
        type: String,
        unique: true,
        sparse: true,
        trim: true,
        maxlength: [20, 'Employee code cannot exceed 20 characters']
    },
    gender: {
        type: String,
        enum: ['Nam', 'Nữ', 'Khác'],
        trim: true
    },
    subDepartment: {
        type: String,
        trim: true,
        maxlength: [100, 'Sub-department cannot exceed 100 characters']
    },
    weight: {
        type: Number,
        min: [0, 'Weight must be positive'],
        max: [500, 'Weight seems unrealistic']
    },
    height: {
        type: Number,
        min: [0, 'Height must be positive'],
        max: [300, 'Height seems unrealistic']
    },
    shirtSize: {
        type: String,
        enum: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'],
        trim: true
    },
    pantSize: {
        type: String,
        trim: true,
        maxlength: [10, 'Pant size cannot exceed 10 characters']
    },
    shoeSize: {
        type: String,
        trim: true,
        maxlength: [10, 'Shoe size cannot exceed 10 characters']
    },
    personalPhone: {
        type: String,
        trim: true,
        match: [/^[0-9+\-\s()]+$/, 'Please enter a valid phone number'],
        maxlength: [20, 'Phone number cannot exceed 20 characters']
    },
    bankAccount: {
        type: String,
        trim: true,
        maxlength: [30, 'Bank account cannot exceed 30 characters']
    },
    lockerNumber: {
        type: String,
        trim: true,
        maxlength: [10, 'Locker number cannot exceed 10 characters']
    },
    employeeStatus: {
        type: String,
        enum: ['Đang làm việc', 'Nghỉ phép', 'Tạm nghỉ', 'Đã nghỉ việc', 'Thử việc'],
        default: 'Đang làm việc'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    lastLogin: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.password;
            delete ret.__v;
            return ret;
        }
    }
});
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ role: 1 });
userSchema.index({ department: 1 });
userSchema.index({ isActive: 1 });
userSchema.index({ employeeCode: 1 });
userSchema.index({ employeeStatus: 1 });
userSchema.index({ gender: 1 });
userSchema.pre('save', async function (next) {
    if (!this.isModified('password'))
        return next();
    try {
        const hashedPassword = await bcryptjs_1.default.hash(this.password, 12);
        this.password = hashedPassword;
        next();
    }
    catch (error) {
        next(error);
    }
});
userSchema.methods.comparePassword = async function (candidatePassword) {
    try {
        return await bcryptjs_1.default.compare(candidatePassword, this.password);
    }
    catch (error) {
        throw new Error('Password comparison failed');
    }
};
userSchema.statics.findByEmailWithPassword = function (email) {
    return this.findOne({ email }).select('+password');
};
const User = mongoose_1.default.model('User', userSchema);
exports.default = User;
//# sourceMappingURL=User.js.map