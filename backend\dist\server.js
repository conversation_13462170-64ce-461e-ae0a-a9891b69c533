"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("@/config/config");
const database_1 = __importDefault(require("@/config/database"));
const app_1 = __importDefault(require("./app"));
const utils_1 = require("@/utils");
class Server {
    constructor() {
        this.app = new app_1.default();
        this.database = database_1.default.getInstance();
    }
    async start() {
        try {
            await this.database.connect();
            this.startTokenCleanupJob();
            const server = this.app.getApp().listen(config_1.config.server.port, () => {
                console.log(`
🚀 Server is running!
📍 Environment: ${config_1.config.server.nodeEnv}
🌐 URL: http://localhost:${config_1.config.server.port}
📊 API: http://localhost:${config_1.config.server.port}/api/${config_1.config.server.apiVersion}
🏥 Health: http://localhost:${config_1.config.server.port}/api/${config_1.config.server.apiVersion}/health
📚 Database: ${config_1.config.database.name}
        `);
            });
            this.setupGracefulShutdown(server);
        }
        catch (error) {
            console.error('❌ Failed to start server:', error);
            process.exit(1);
        }
    }
    startTokenCleanupJob() {
        setInterval(async () => {
            try {
                await utils_1.JWTUtils.cleanupExpiredTokens();
                console.log('🧹 Cleaned up expired tokens');
            }
            catch (error) {
                console.error('❌ Token cleanup failed:', error);
            }
        }, 60 * 60 * 1000);
    }
    setupGracefulShutdown(server) {
        const gracefulShutdown = async (signal) => {
            console.log(`\n📡 Received ${signal}. Starting graceful shutdown...`);
            server.close(async () => {
                console.log('🔌 HTTP server closed');
                try {
                    await this.database.disconnect();
                    console.log('💾 Database connection closed');
                    console.log('✅ Graceful shutdown completed');
                    process.exit(0);
                }
                catch (error) {
                    console.error('❌ Error during shutdown:', error);
                    process.exit(1);
                }
            });
            setTimeout(() => {
                console.error('⏰ Forced shutdown after timeout');
                process.exit(1);
            }, 30000);
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('uncaughtException', (error) => {
            console.error('💥 Uncaught Exception:', error);
            gracefulShutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason, promise) => {
            console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
            gracefulShutdown('unhandledRejection');
        });
    }
}
const server = new Server();
server.start();
//# sourceMappingURL=server.js.map