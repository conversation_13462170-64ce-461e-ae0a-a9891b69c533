"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const responsibilityController_1 = __importDefault(require("@/controllers/responsibilityController"));
const responsibilityValidators_1 = require("@/validators/responsibilityValidators");
const middleware_1 = require("@/middleware");
const types_1 = require("@/types");
const router = (0, express_1.Router)();
router.use(middleware_1.authenticate);
router.get('/', (0, middleware_1.authorize)(types_1.UserRole.ADMIN, types_1.UserRole.MANAGER), responsibilityValidators_1.responsibilityQueryValidation, middleware_1.validateRequest, responsibilityController_1.default.getAllResponsibilities);
router.get('/stats', (0, middleware_1.authorize)(types_1.UserRole.ADMIN, types_1.UserRole.MANAGER), responsibilityController_1.default.getResponsibilityStats);
router.get('/code/:code', (0, middleware_1.authorize)(types_1.UserRole.ADMIN, types_1.UserRole.MANAGER), responsibilityValidators_1.getResponsibilityByCodeValidation, middleware_1.validateRequest, responsibilityController_1.default.getResponsibilityByCode);
router.get('/:id', (0, middleware_1.authorize)(types_1.UserRole.ADMIN, types_1.UserRole.MANAGER), responsibilityValidators_1.getResponsibilityValidation, middleware_1.validateRequest, responsibilityController_1.default.getResponsibilityById);
router.post('/', (0, middleware_1.authorize)(types_1.UserRole.ADMIN), responsibilityValidators_1.createResponsibilityValidation, middleware_1.validateRequest, responsibilityController_1.default.createResponsibility);
router.put('/:id', (0, middleware_1.authorize)(types_1.UserRole.ADMIN), responsibilityValidators_1.updateResponsibilityValidation, middleware_1.validateRequest, responsibilityController_1.default.updateResponsibility);
router.delete('/:id', (0, middleware_1.authorize)(types_1.UserRole.ADMIN), responsibilityValidators_1.getResponsibilityValidation, middleware_1.validateRequest, responsibilityController_1.default.deleteResponsibility);
exports.default = router;
//# sourceMappingURL=responsibilityRoutes.js.map