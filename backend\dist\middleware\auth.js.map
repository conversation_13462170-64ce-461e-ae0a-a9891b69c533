{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,mCAAyD;AACzD,mCAAkD;AAClD,qCAAgC;AAKzB,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,qBAAa,CAAC,YAAY,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC;YAC5D,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAGtC,MAAM,OAAO,GAAG,gBAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAGlD,MAAM,IAAI,GAAG,MAAM,aAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAEjD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,qBAAa,CAAC,YAAY,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;YAClD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,qBAAa,CAAC,YAAY,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,qBAAa,CAAC,YAAY,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC;IAC9D,CAAC;AACH,CAAC,CAAC;AArCW,QAAA,YAAY,gBAqCvB;AAKK,MAAM,SAAS,GAAG,CAAC,GAAG,KAAiB,EAAE,EAAE;IAChD,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,qBAAa,CAAC,YAAY,CAAC,GAAG,EAAE,yBAAyB,CAAC,CAAC;YAC3D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAgB,CAAC,EAAE,CAAC;YAC/C,qBAAa,CAAC,SAAS,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC;YACzD,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAdW,QAAA,SAAS,aAcpB;AAKW,QAAA,YAAY,GAAG,IAAA,iBAAS,EAAC,gBAAQ,CAAC,KAAK,CAAC,CAAC;AAKzC,QAAA,qBAAqB,GAAG,IAAA,iBAAS,EAAC,gBAAQ,CAAC,KAAK,EAAE,gBAAQ,CAAC,OAAO,CAAC,CAAC;AAK1E,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,OAAO,GAAG,gBAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,IAAI,GAAG,MAAM,aAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAEjD,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;YAClB,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAEf,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC;AAvBW,QAAA,YAAY,gBAuBvB"}