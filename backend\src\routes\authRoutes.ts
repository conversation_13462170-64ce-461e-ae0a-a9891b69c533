import { Router } from 'express';
import { AuthController } from '@/controllers';
import { authenticate, asyncHandler, validate } from '@/middleware';
import {
  loginValidation,
  refreshTokenValidation,
  changePasswordValidation,
  updateProfileValidation
} from '@/validators';

const router = Router();

// Public routes
router.post('/login',
  validate(loginValidation),
  asyncHandler(AuthController.login)
);

router.post('/refresh-token',
  validate(refreshTokenValidation),
  asyncHandler(AuthController.refreshToken)
);

// Protected routes
router.post('/logout',
  authenticate,
  asyncHandler(AuthController.logout)
);

router.post('/logout-all',
  authenticate,
  asyncHandler(AuthController.logoutAll)
);

router.get('/profile',
  authenticate,
  asyncHandler(AuthController.getProfile)
);

router.put('/profile',
  authenticate,
  validate(updateProfileValidation),
  asyncHandler(AuthController.updateProfile)
);

router.put('/change-password',
  authenticate,
  validate(changePasswordValidation),
  asyncHandler(AuthController.changePassword)
);



export default router;
