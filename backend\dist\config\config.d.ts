interface Config {
    server: {
        port: number;
        nodeEnv: string;
        apiVersion: string;
    };
    database: {
        uri: string;
        name: string;
    };
    jwt: {
        secret: string;
        expiresIn: string;
        refreshSecret: string;
        refreshExpiresIn: string;
    };
    security: {
        bcryptSaltRounds: number;
        rateLimitWindowMs: number;
        rateLimitMaxRequests: number;
    };
    cors: {
        origin: string;
    };
    logging: {
        level: string;
    };
}
declare const config: Config;
export { config };
//# sourceMappingURL=config.d.ts.map