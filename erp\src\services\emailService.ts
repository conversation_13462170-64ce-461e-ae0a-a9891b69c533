// Simple Email Service for sending passwords to new employees

interface SendPasswordEmailParams {
  email: string;
  fullName: string;
  username: string;
  password: string;
  employeeCode: string;
}

// Enhanced mock email service with realistic simulation
export const sendPasswordEmail = async (params: SendPasswordEmailParams): Promise<boolean> => {
  const { email, fullName, username, password, employeeCode } = params;
  
  try {
    console.log('🚀 Đang gửi email...');
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate email sending
    console.log('📧 ===== EMAIL ĐƯỢC GỬI THÀNH CÔNG =====');
    console.log('📧 Người nhận:', email);
    console.log('📧 Tiêu đề: Thông tin tài khoản mới - Mã NV:', employeeCode);
    console.log('📧 Nội dung email:');
    console.log(`
    🎉 Chào mừng ${fullName} đến với công ty!
    
    📋 Thông tin tài khoản:
    ✅ Mã nhân viên: ${employeeCode}
    ✅ Tài khoản: ${username}
    ✅ Mật khẩu: ${password}
    ✅ Email: ${email}
    
    📝 Hướng dẫn:
    1. Đăng nhập vào hệ thống ERP
    2. Đổi mật khẩu ngay lần đầu đăng nhập
    3. Liên hệ IT nếu gặp vấn đề
    
    ⚠️ Lưu ý bảo mật:
    - Không chia sẻ thông tin đăng nhập
    - Đăng xuất sau khi sử dụng
    `);
    console.log('📧 =====================================');
    
    // Show detailed success message to user
    alert(`📧 EMAIL ĐÃ ĐƯỢC GỬI THÀNH CÔNG!

📨 Gửi đến: ${email}
👤 Người nhận: ${fullName}
🆔 Mã nhân viên: ${employeeCode}
🔑 Tài khoản: ${username}
🔐 Mật khẩu: ${password}

✅ Thông tin đăng nhập đã được gửi về email của nhân viên.

📝 Nhân viên cần:
1. Kiểm tra hộp thư email (kể cả spam)
2. Đăng nhập với thông tin trên
3. Đổi mật khẩu ngay lần đầu đăng nhập

⚠️ LƯU Ý: Đây là mô phỏng gửi email.
Trong thực tế, email sẽ được gửi qua hệ thống email thật.`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Lỗi gửi email:', error);
    alert('❌ Có lỗi xảy ra khi gửi email!\nVui lòng thông báo thông tin đăng nhập cho nhân viên.');
    return false;
  }
};

// Function to create email template (for future real email integration)
export const createEmailTemplate = (params: SendPasswordEmailParams): string => {
  const { fullName, username, password, employeeCode } = params;
  
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: #4F46E5; color: white; padding: 20px; text-align: center;">
        <h1>🎉 Chào mừng bạn đến với công ty!</h1>
      </div>
      
      <div style="padding: 30px; background: #f9f9f9;">
        <h2>Xin chào ${fullName},</h2>
        
        <p>Tài khoản của bạn đã được tạo thành công trong hệ thống ERP.</p>
        
        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>📋 Thông tin tài khoản:</h3>
          <p><strong>Mã nhân viên:</strong> ${employeeCode}</p>
          <p><strong>Tài khoản:</strong> ${username}</p>
          <p><strong>Mật khẩu:</strong> <code style="background: #f0f0f0; padding: 4px 8px;">${password}</code></p>
        </div>
        
        <div style="background: #FEF3C7; padding: 15px; border-radius: 8px;">
          <h4>📝 Hướng dẫn đăng nhập:</h4>
          <ol>
            <li>Truy cập hệ thống ERP</li>
            <li>Sử dụng thông tin đăng nhập ở trên</li>
            <li>Đổi mật khẩu ngay sau lần đăng nhập đầu tiên</li>
          </ol>
        </div>
        
        <div style="background: #FEE2E2; padding: 15px; border-radius: 8px; margin-top: 20px;">
          <h4 style="color: #DC2626;">⚠️ Lưu ý bảo mật:</h4>
          <ul style="color: #7F1D1D;">
            <li>Không chia sẻ thông tin đăng nhập</li>
            <li>Đổi mật khẩu định kỳ</li>
            <li>Đăng xuất sau khi sử dụng</li>
          </ul>
        </div>
      </div>
      
      <div style="text-align: center; padding: 20px; color: #666;">
        <p>Email tự động từ hệ thống ERP</p>
      </div>
    </div>
  `;
};

// For future real email integration
export const sendRealEmail = async (params: SendPasswordEmailParams): Promise<boolean> => {
  try {
    // Example with fetch to backend API
    const response = await fetch('/api/send-password-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: params.email,
        subject: `Thông tin tài khoản mới - Mã NV: ${params.employeeCode}`,
        html: createEmailTemplate(params)
      })
    });
    
    return response.ok;
  } catch (error) {
    console.error('Real email error:', error);
    return false;
  }
};
