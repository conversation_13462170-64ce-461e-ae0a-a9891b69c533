import { Request, Response } from 'express';
declare class ResponsibilityController {
    static getAllResponsibilities(req: Request, res: Response): Promise<void>;
    static createResponsibility(req: Request, res: Response): Promise<void>;
    static getResponsibilityById(req: Request, res: Response): Promise<void>;
    static getResponsibilityByCode(req: Request, res: Response): Promise<void>;
    static updateResponsibility(req: Request, res: Response): Promise<void>;
    static deleteResponsibility(req: Request, res: Response): Promise<void>;
    static getResponsibilityStats(req: Request, res: Response): Promise<void>;
}
export default ResponsibilityController;
//# sourceMappingURL=responsibilityController.d.ts.map