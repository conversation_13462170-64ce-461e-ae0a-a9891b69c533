"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("@/controllers");
const middleware_1 = require("@/middleware");
const validators_1 = require("@/validators");
const router = (0, express_1.Router)();
router.post('/login', (0, middleware_1.validate)(validators_1.loginValidation), (0, middleware_1.asyncHandler)(controllers_1.AuthController.login));
router.post('/refresh-token', (0, middleware_1.validate)(validators_1.refreshTokenValidation), (0, middleware_1.asyncHandler)(controllers_1.AuthController.refreshToken));
router.post('/logout', middleware_1.authenticate, (0, middleware_1.asyncHandler)(controllers_1.AuthController.logout));
router.post('/logout-all', middleware_1.authenticate, (0, middleware_1.asyncHandler)(controllers_1.AuthController.logoutAll));
router.get('/profile', middleware_1.authenticate, (0, middleware_1.asyncHandler)(controllers_1.AuthController.getProfile));
router.put('/profile', middleware_1.authenticate, (0, middleware_1.validate)(validators_1.updateProfileValidation), (0, middleware_1.asyncHandler)(controllers_1.AuthController.updateProfile));
router.put('/change-password', middleware_1.authenticate, (0, middleware_1.validate)(validators_1.changePasswordValidation), (0, middleware_1.asyncHandler)(controllers_1.AuthController.changePassword));
exports.default = router;
//# sourceMappingURL=authRoutes.js.map