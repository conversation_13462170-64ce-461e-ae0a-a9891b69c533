"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class ResponseUtils {
    static success(res, data, message = 'Success', statusCode = 200) {
        const response = {
            success: true,
            message,
            data,
            timestamp: new Date().toISOString()
        };
        return res.status(statusCode).json(response);
    }
    static error(res, message = 'Internal Server Error', statusCode = 500, error) {
        const response = {
            success: false,
            message,
            error,
            timestamp: new Date().toISOString()
        };
        return res.status(statusCode).json(response);
    }
    static paginated(res, data, currentPage, totalItems, itemsPerPage, message = 'Success') {
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        const paginatedResponse = {
            data,
            pagination: {
                currentPage,
                totalPages,
                totalItems,
                itemsPerPage,
                hasNextPage: currentPage < totalPages,
                hasPrevPage: currentPage > 1
            }
        };
        const response = {
            success: true,
            message,
            data: paginatedResponse,
            timestamp: new Date().toISOString()
        };
        return res.status(200).json(response);
    }
    static created(res, data, message = 'Resource created successfully') {
        return this.success(res, data, message, 201);
    }
    static noContent(res) {
        return res.status(204).send();
    }
    static badRequest(res, message = 'Bad Request', error) {
        return this.error(res, message, 400, error);
    }
    static unauthorized(res, message = 'Unauthorized') {
        return this.error(res, message, 401);
    }
    static forbidden(res, message = 'Forbidden') {
        return this.error(res, message, 403);
    }
    static notFound(res, message = 'Resource not found') {
        return this.error(res, message, 404);
    }
    static conflict(res, message = 'Conflict', error) {
        return this.error(res, message, 409, error);
    }
    static validationError(res, errors, message = 'Validation failed') {
        return this.error(res, message, 422, JSON.stringify(errors));
    }
}
exports.default = ResponseUtils;
//# sourceMappingURL=response.js.map