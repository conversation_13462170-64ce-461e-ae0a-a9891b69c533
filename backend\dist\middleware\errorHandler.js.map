{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AAEA,mCAAwC;AACxC,4CAAyC;AAKlC,MAAM,YAAY,GAAG,CAC1B,KAAkB,EAClB,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC;IACzC,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,uBAAuB,CAAC;IAGvD,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE;QACtB,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;IAGH,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACrC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,kBAAkB,CAAC;IAC/B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QACtC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,mBAAmB,CAAC;IAChC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,IAAK,KAAa,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;QAC9E,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,uBAAuB,CAAC;IACpC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC9C,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,eAAe,CAAC;IAC5B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC9C,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,eAAe,CAAC;IAC5B,CAAC;IAGD,MAAM,YAAY,GAAG,eAAM,CAAC,MAAM,CAAC,OAAO,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;IAEvF,qBAAa,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;AAC9D,CAAC,CAAC;AA1CW,QAAA,YAAY,gBA0CvB;AAKK,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAQ,EAAE;IACnE,qBAAa,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,GAAG,CAAC,WAAW,YAAY,CAAC,CAAC;AACpE,CAAC,CAAC;AAFW,QAAA,eAAe,mBAE1B;AAKK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB"}