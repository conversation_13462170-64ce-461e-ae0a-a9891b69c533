"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.employeeQueryValidation = exports.resetPasswordValidation = exports.getEmployeeByCodeValidation = exports.getEmployeeValidation = exports.updateEmployeeValidation = exports.createEmployeeValidation = void 0;
const express_validator_1 = require("express-validator");
const types_1 = require("@/types");
exports.createEmployeeValidation = [
    (0, express_validator_1.body)('username')
        .trim()
        .isLength({ min: 3, max: 30 })
        .withMessage('Username must be between 3 and 30 characters')
        .matches(/^[a-zA-Z0-9_\u00C0-\u017F\u1EA0-\u1EF9]+$/)
        .withMessage('Username can only contain letters, numbers, underscores, and Vietnamese characters'),
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    (0, express_validator_1.body)('password')
        .optional()
        .isLength({ min: 6 })
        .withMessage('Password must be at least 6 characters long'),
    (0, express_validator_1.body)('firstName')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('First name is required and must not exceed 50 characters')
        .matches(/^[a-zA-ZÀ-ÿ\u00C0-\u017F\u1EA0-\u1EF9\s]+$/)
        .withMessage('First name can only contain letters, spaces, and Vietnamese characters'),
    (0, express_validator_1.body)('lastName')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Last name is required and must not exceed 50 characters')
        .matches(/^[a-zA-ZÀ-ÿ\u00C0-\u017F\u1EA0-\u1EF9\s]+$/)
        .withMessage('Last name can only contain letters, spaces, and Vietnamese characters'),
    (0, express_validator_1.body)('role')
        .optional()
        .isIn(Object.values(types_1.UserRole))
        .withMessage('Invalid role specified'),
    (0, express_validator_1.body)('department')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Department must not exceed 100 characters'),
    (0, express_validator_1.body)('position')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Position must not exceed 100 characters'),
    (0, express_validator_1.body)('employeeCode')
        .optional()
        .trim()
        .isLength({ max: 20 })
        .withMessage('Employee code must not exceed 20 characters')
        .matches(/^[a-zA-Z0-9\-_]+$/)
        .withMessage('Employee code can only contain letters, numbers, hyphens, and underscores'),
    (0, express_validator_1.body)('gender')
        .optional()
        .isIn(['Nam', 'Nữ', 'Khác'])
        .withMessage('Gender must be Nam, Nữ, or Khác'),
    (0, express_validator_1.body)('subDepartment')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Sub-department must not exceed 100 characters'),
    (0, express_validator_1.body)('weight')
        .optional()
        .isFloat({ min: 0, max: 500 })
        .withMessage('Weight must be between 0 and 500 kg'),
    (0, express_validator_1.body)('height')
        .optional()
        .isFloat({ min: 0, max: 300 })
        .withMessage('Height must be between 0 and 300 cm'),
    (0, express_validator_1.body)('shirtSize')
        .optional()
        .isIn(['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'])
        .withMessage('Shirt size must be XS, S, M, L, XL, XXL, or XXXL'),
    (0, express_validator_1.body)('pantSize')
        .optional()
        .trim()
        .isLength({ max: 10 })
        .withMessage('Pant size must not exceed 10 characters'),
    (0, express_validator_1.body)('shoeSize')
        .optional()
        .trim()
        .isLength({ max: 10 })
        .withMessage('Shoe size must not exceed 10 characters'),
    (0, express_validator_1.body)('personalPhone')
        .optional()
        .trim()
        .matches(/^[0-9+\-\s()]+$/)
        .withMessage('Please enter a valid phone number')
        .isLength({ max: 20 })
        .withMessage('Phone number must not exceed 20 characters'),
    (0, express_validator_1.body)('bankAccount')
        .optional()
        .trim()
        .isLength({ max: 30 })
        .withMessage('Bank account must not exceed 30 characters'),
    (0, express_validator_1.body)('lockerNumber')
        .optional()
        .trim()
        .isLength({ max: 10 })
        .withMessage('Locker number must not exceed 10 characters'),
    (0, express_validator_1.body)('employeeStatus')
        .optional()
        .isIn(['Đang làm việc', 'Nghỉ phép', 'Tạm nghỉ', 'Đã nghỉ việc', 'Thử việc'])
        .withMessage('Invalid employee status'),
    (0, express_validator_1.body)('isActive')
        .optional()
        .isBoolean()
        .withMessage('isActive must be a boolean value')
];
exports.updateEmployeeValidation = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('Invalid employee ID'),
    (0, express_validator_1.body)('username')
        .optional()
        .trim()
        .isLength({ min: 3, max: 30 })
        .withMessage('Username must be between 3 and 30 characters')
        .matches(/^[a-zA-Z0-9_\u00C0-\u017F\u1EA0-\u1EF9]+$/)
        .withMessage('Username can only contain letters, numbers, underscores, and Vietnamese characters'),
    (0, express_validator_1.body)('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    (0, express_validator_1.body)('firstName')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('First name must not exceed 50 characters')
        .matches(/^[a-zA-ZÀ-ÿ\u00C0-\u017F\u1EA0-\u1EF9\s]+$/)
        .withMessage('First name can only contain letters, spaces, and Vietnamese characters'),
    (0, express_validator_1.body)('lastName')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Last name must not exceed 50 characters')
        .matches(/^[a-zA-ZÀ-ÿ\u00C0-\u017F\u1EA0-\u1EF9\s]+$/)
        .withMessage('Last name can only contain letters, spaces, and Vietnamese characters'),
    (0, express_validator_1.body)('role')
        .optional()
        .isIn(Object.values(types_1.UserRole))
        .withMessage('Invalid role specified'),
    (0, express_validator_1.body)('department')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Department must not exceed 100 characters'),
    (0, express_validator_1.body)('position')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Position must not exceed 100 characters'),
    (0, express_validator_1.body)('employeeCode')
        .optional()
        .trim()
        .isLength({ max: 20 })
        .withMessage('Employee code must not exceed 20 characters')
        .matches(/^[a-zA-Z0-9\-_]+$/)
        .withMessage('Employee code can only contain letters, numbers, hyphens, and underscores'),
    (0, express_validator_1.body)('gender')
        .optional()
        .isIn(['Nam', 'Nữ', 'Khác'])
        .withMessage('Gender must be Nam, Nữ, or Khác'),
    (0, express_validator_1.body)('subDepartment')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Sub-department must not exceed 100 characters'),
    (0, express_validator_1.body)('weight')
        .optional()
        .isFloat({ min: 0, max: 500 })
        .withMessage('Weight must be between 0 and 500 kg'),
    (0, express_validator_1.body)('height')
        .optional()
        .isFloat({ min: 0, max: 300 })
        .withMessage('Height must be between 0 and 300 cm'),
    (0, express_validator_1.body)('shirtSize')
        .optional()
        .isIn(['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'])
        .withMessage('Shirt size must be XS, S, M, L, XL, XXL, or XXXL'),
    (0, express_validator_1.body)('pantSize')
        .optional()
        .trim()
        .isLength({ max: 10 })
        .withMessage('Pant size must not exceed 10 characters'),
    (0, express_validator_1.body)('shoeSize')
        .optional()
        .trim()
        .isLength({ max: 10 })
        .withMessage('Shoe size must not exceed 10 characters'),
    (0, express_validator_1.body)('personalPhone')
        .optional()
        .trim()
        .matches(/^[0-9+\-\s()]+$/)
        .withMessage('Please enter a valid phone number')
        .isLength({ max: 20 })
        .withMessage('Phone number must not exceed 20 characters'),
    (0, express_validator_1.body)('bankAccount')
        .optional()
        .trim()
        .isLength({ max: 30 })
        .withMessage('Bank account must not exceed 30 characters'),
    (0, express_validator_1.body)('lockerNumber')
        .optional()
        .trim()
        .isLength({ max: 10 })
        .withMessage('Locker number must not exceed 10 characters'),
    (0, express_validator_1.body)('employeeStatus')
        .optional()
        .isIn(['Đang làm việc', 'Nghỉ phép', 'Tạm nghỉ', 'Đã nghỉ việc', 'Thử việc'])
        .withMessage('Invalid employee status'),
    (0, express_validator_1.body)('isActive')
        .optional()
        .isBoolean()
        .withMessage('isActive must be a boolean value')
];
exports.getEmployeeValidation = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('Invalid employee ID')
];
exports.getEmployeeByCodeValidation = [
    (0, express_validator_1.param)('code')
        .trim()
        .isLength({ min: 1, max: 20 })
        .withMessage('Employee code must be between 1 and 20 characters')
];
exports.resetPasswordValidation = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('Invalid employee ID'),
    (0, express_validator_1.body)('newPassword')
        .optional()
        .isLength({ min: 6 })
        .withMessage('Password must be at least 6 characters long')
];
exports.employeeQueryValidation = [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    (0, express_validator_1.query)('department')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Department filter must not exceed 100 characters'),
    (0, express_validator_1.query)('subDepartment')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Sub-department filter must not exceed 100 characters'),
    (0, express_validator_1.query)('gender')
        .optional()
        .isIn(['Nam', 'Nữ', 'Khác'])
        .withMessage('Gender filter must be Nam, Nữ, or Khác'),
    (0, express_validator_1.query)('employeeStatus')
        .optional()
        .isIn(['Đang làm việc', 'Nghỉ phép', 'Tạm nghỉ', 'Đã nghỉ việc', 'Thử việc'])
        .withMessage('Invalid employee status filter'),
    (0, express_validator_1.query)('isActive')
        .optional()
        .isBoolean()
        .withMessage('isActive filter must be a boolean value'),
    (0, express_validator_1.query)('search')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Search term must not exceed 100 characters')
];
//# sourceMappingURL=employeeValidators.js.map