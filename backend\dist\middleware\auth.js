"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.requireManagerOrAdmin = exports.requireAdmin = exports.authorize = exports.authenticate = void 0;
const types_1 = require("@/types");
const utils_1 = require("@/utils");
const models_1 = require("@/models");
const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            utils_1.ResponseUtils.unauthorized(res, 'Access token is required');
            return;
        }
        const token = authHeader.substring(7);
        const decoded = utils_1.JWTUtils.verifyAccessToken(token);
        const user = await models_1.User.findById(decoded.userId);
        if (!user) {
            utils_1.ResponseUtils.unauthorized(res, 'User not found');
            return;
        }
        if (!user.isActive) {
            utils_1.ResponseUtils.unauthorized(res, 'Account is deactivated');
            return;
        }
        req.user = user;
        next();
    }
    catch (error) {
        utils_1.ResponseUtils.unauthorized(res, 'Invalid or expired token');
    }
};
exports.authenticate = authenticate;
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            utils_1.ResponseUtils.unauthorized(res, 'Authentication required');
            return;
        }
        if (!roles.includes(req.user.role)) {
            utils_1.ResponseUtils.forbidden(res, 'Insufficient permissions');
            return;
        }
        next();
    };
};
exports.authorize = authorize;
exports.requireAdmin = (0, exports.authorize)(types_1.UserRole.ADMIN);
exports.requireManagerOrAdmin = (0, exports.authorize)(types_1.UserRole.ADMIN, types_1.UserRole.MANAGER);
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            const decoded = utils_1.JWTUtils.verifyAccessToken(token);
            const user = await models_1.User.findById(decoded.userId);
            if (user && user.isActive) {
                req.user = user;
            }
        }
        next();
    }
    catch (error) {
        next();
    }
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map