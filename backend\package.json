{"name": "erp-backend", "version": "1.0.0", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon", "dev:watch": "concurrently \"tsc -w\" \"nodemon dist/server.js\"", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "create-admin": "node scripts/create-admin.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["erp", "backend", "typescript", "express", "mongodb"], "author": "", "license": "ISC", "description": "ERP System Backend API", "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0", "ms": "^2.1.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.10", "@types/ms": "^2.1.0", "@types/node": "^24.0.0", "concurrently": "^9.1.2", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}