import { Request, Response } from 'express';
import { User, RefreshToken } from '@/models';
import { JWTUtils, ResponseUtils } from '@/utils';
import { AuthenticatedRequest, LoginRequest, RefreshTokenRequest } from '@/types';

class AuthController {


  /**
   * Login user
   */
  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { email, password }: LoginRequest = req.body;

      // Find user with password
      const user = await User.findOne({ email }).select('+password');

      if (!user) {
        ResponseUtils.unauthorized(res, 'Invalid email or password');
        return;
      }

      // Check if user is active
      if (!user.isActive) {
        ResponseUtils.unauthorized(res, 'Account is deactivated');
        return;
      }

      // Verify password
      const isPasswordValid = await user.comparePassword(password);

      if (!isPasswordValid) {
        ResponseUtils.unauthorized(res, 'Invalid email or password');
        return;
      }

      // Update last login
      user.lastLogin = new Date();
      await user.save();

      // Generate tokens
      const { accessToken, refreshToken } = await JWTUtils.generateTokenPair(user);

      // Remove password from response
      const userResponse = user.toJSON();

      ResponseUtils.success(res, {
        user: userResponse,
        accessToken,
        refreshToken
      }, 'Login successful');

    } catch (error) {
      console.error('Login error:', error);
      ResponseUtils.error(res, 'Login failed');
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken }: RefreshTokenRequest = req.body;

      // Verify refresh token
      const userId = await JWTUtils.verifyRefreshToken(refreshToken);

      // Get user
      const user = await User.findById(userId);

      if (!user || !user.isActive) {
        ResponseUtils.unauthorized(res, 'User not found or inactive');
        return;
      }

      // Revoke old refresh token
      await JWTUtils.revokeRefreshToken(refreshToken);

      // Generate new tokens
      const { accessToken, refreshToken: newRefreshToken } = await JWTUtils.generateTokenPair(user);

      ResponseUtils.success(res, {
        accessToken,
        refreshToken: newRefreshToken
      }, 'Token refreshed successfully');

    } catch (error) {
      console.error('Token refresh error:', error);
      ResponseUtils.unauthorized(res, 'Invalid or expired refresh token');
    }
  }

  /**
   * Logout user
   */
  static async logout(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (refreshToken) {
        await JWTUtils.revokeRefreshToken(refreshToken);
      }

      ResponseUtils.success(res, null, 'Logout successful');

    } catch (error) {
      console.error('Logout error:', error);
      ResponseUtils.error(res, 'Logout failed');
    }
  }

  /**
   * Logout from all devices
   */
  static async logoutAll(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        ResponseUtils.unauthorized(res, 'User not authenticated');
        return;
      }

      await JWTUtils.revokeAllUserTokens(req.user._id);

      ResponseUtils.success(res, null, 'Logged out from all devices');

    } catch (error) {
      console.error('Logout all error:', error);
      ResponseUtils.error(res, 'Logout failed');
    }
  }

  /**
   * Get current user profile
   */
  static async getProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        ResponseUtils.unauthorized(res, 'User not authenticated');
        return;
      }

      ResponseUtils.success(res, req.user, 'Profile retrieved successfully');

    } catch (error) {
      console.error('Get profile error:', error);
      ResponseUtils.error(res, 'Failed to retrieve profile');
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        ResponseUtils.unauthorized(res, 'User not authenticated');
        return;
      }

      const allowedUpdates = ['firstName', 'lastName', 'department', 'position'];
      const updates = Object.keys(req.body)
        .filter(key => allowedUpdates.includes(key))
        .reduce((obj: any, key) => {
          obj[key] = req.body[key];
          return obj;
        }, {});

      const updatedUser = await User.findByIdAndUpdate(
        req.user._id,
        updates,
        { new: true, runValidators: true }
      );

      ResponseUtils.success(res, updatedUser, 'Profile updated successfully');

    } catch (error) {
      console.error('Update profile error:', error);
      ResponseUtils.error(res, 'Failed to update profile');
    }
  }

  /**
   * Change password
   */
  static async changePassword(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        ResponseUtils.unauthorized(res, 'User not authenticated');
        return;
      }

      const { currentPassword, newPassword } = req.body;

      // Get user with password
      const user = await User.findById(req.user._id).select('+password');

      if (!user) {
        ResponseUtils.notFound(res, 'User not found');
        return;
      }

      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);

      if (!isCurrentPasswordValid) {
        ResponseUtils.badRequest(res, 'Current password is incorrect');
        return;
      }

      // Update password
      user.password = newPassword;
      await user.save();

      // Revoke all refresh tokens to force re-login on all devices
      await JWTUtils.revokeAllUserTokens(user._id);

      ResponseUtils.success(res, null, 'Password changed successfully');

    } catch (error) {
      console.error('Change password error:', error);
      ResponseUtils.error(res, 'Failed to change password');
    }
  }


}

export default AuthController;
