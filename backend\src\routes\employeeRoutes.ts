import { Router } from 'express';
import EmployeeController from '@/controllers/employeeController';
import { 
  createEmployeeValidation,
  updateEmployeeValidation,
  getEmployeeValidation,
  getEmployeeByCodeValidation,
  resetPasswordValidation,
  employeeQueryValidation
} from '@/validators';
import { authenticate, authorize, validateRequest } from '@/middleware';
import { UserRole } from '@/types';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @route   GET /api/v1/employees
 * @desc    Get all employees with pagination and filtering
 * @access  Admin, Manager
 */
router.get(
  '/',
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  employeeQueryValidation,
  validateRequest,
  EmployeeController.getAllEmployees
);

/**
 * @route   GET /api/v1/employees/stats
 * @desc    Get employee statistics
 * @access  Admin, Manager
 */
router.get(
  '/stats',
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  EmployeeController.getEmployeeStats
);

/**
 * @route   GET /api/v1/employees/code/:code
 * @desc    Get employee by employee code
 * @access  Admin, Manager
 */
router.get(
  '/code/:code',
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  getEmployeeByCodeValidation,
  validateRequest,
  EmployeeController.getEmployeeByCode
);

/**
 * @route   GET /api/v1/employees/:id
 * @desc    Get employee by ID
 * @access  Admin, Manager
 */
router.get(
  '/:id',
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  getEmployeeValidation,
  validateRequest,
  EmployeeController.getEmployeeById
);

/**
 * @route   POST /api/v1/employees
 * @desc    Create new employee
 * @access  Admin only
 */
router.post(
  '/',
  authorize(UserRole.ADMIN),
  createEmployeeValidation,
  validateRequest,
  EmployeeController.createEmployee
);

/**
 * @route   PUT /api/v1/employees/:id
 * @desc    Update employee
 * @access  Admin only
 */
router.put(
  '/:id',
  authorize(UserRole.ADMIN),
  updateEmployeeValidation,
  validateRequest,
  EmployeeController.updateEmployee
);

/**
 * @route   DELETE /api/v1/employees/:id
 * @desc    Delete employee (soft delete)
 * @access  Admin only
 */
router.delete(
  '/:id',
  authorize(UserRole.ADMIN),
  getEmployeeValidation,
  validateRequest,
  EmployeeController.deleteEmployee
);

/**
 * @route   POST /api/v1/employees/:id/reset-password
 * @desc    Reset employee password
 * @access  Admin only
 */
router.post(
  '/:id/reset-password',
  authorize(UserRole.ADMIN),
  resetPasswordValidation,
  validateRequest,
  EmployeeController.resetEmployeePassword
);

export default router;
