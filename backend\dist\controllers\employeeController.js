"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const models_1 = require("@/models");
const utils_1 = require("@/utils");
class EmployeeController {
    static async getAllEmployees(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const skip = (page - 1) * limit;
            const filter = {};
            if (req.query.department) {
                filter.department = new RegExp(req.query.department, 'i');
            }
            if (req.query.subDepartment) {
                filter.subDepartment = new RegExp(req.query.subDepartment, 'i');
            }
            if (req.query.gender) {
                filter.gender = req.query.gender;
            }
            if (req.query.employeeStatus) {
                filter.employeeStatus = req.query.employeeStatus;
            }
            if (req.query.isActive !== undefined) {
                filter.isActive = req.query.isActive === 'true';
            }
            if (req.query.search) {
                const searchRegex = new RegExp(req.query.search, 'i');
                filter.$or = [
                    { firstName: searchRegex },
                    { lastName: searchRegex },
                    { email: searchRegex },
                    { employeeCode: searchRegex },
                    { username: searchRegex }
                ];
            }
            const employees = await models_1.User.find(filter)
                .select('-password')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit);
            const totalEmployees = await models_1.User.countDocuments(filter);
            utils_1.ResponseUtils.paginated(res, employees, page, totalEmployees, limit, 'Employees retrieved successfully');
        }
        catch (error) {
            console.error('Get all employees error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to retrieve employees');
        }
    }
    static async getEmployeeById(req, res) {
        try {
            const { id } = req.params;
            const employee = await models_1.User.findById(id).select('-password');
            if (!employee) {
                utils_1.ResponseUtils.notFound(res, 'Employee not found');
                return;
            }
            utils_1.ResponseUtils.success(res, employee, 'Employee retrieved successfully');
        }
        catch (error) {
            console.error('Get employee by ID error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to retrieve employee');
        }
    }
    static async getEmployeeByCode(req, res) {
        try {
            const { code } = req.params;
            const employee = await models_1.User.findOne({ employeeCode: code }).select('-password');
            if (!employee) {
                utils_1.ResponseUtils.notFound(res, 'Employee not found');
                return;
            }
            utils_1.ResponseUtils.success(res, employee, 'Employee retrieved successfully');
        }
        catch (error) {
            console.error('Get employee by code error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to retrieve employee');
        }
    }
    static async createEmployee(req, res) {
        try {
            const employeeData = req.body;
            if (employeeData.employeeCode) {
                const existingEmployee = await models_1.User.findOne({ employeeCode: employeeData.employeeCode });
                if (existingEmployee) {
                    utils_1.ResponseUtils.conflict(res, 'Employee code already exists');
                    return;
                }
            }
            const existingEmail = await models_1.User.findOne({ email: employeeData.email });
            if (existingEmail) {
                utils_1.ResponseUtils.conflict(res, 'Email already exists');
                return;
            }
            const existingUsername = await models_1.User.findOne({ username: employeeData.username });
            if (existingUsername) {
                utils_1.ResponseUtils.conflict(res, 'Username already exists');
                return;
            }
            if (!employeeData.password) {
                employeeData.password = 'Employee123!';
            }
            const employee = new models_1.User(employeeData);
            await employee.save();
            const employeeResponse = employee.toJSON();
            utils_1.ResponseUtils.created(res, employeeResponse, 'Employee created successfully');
        }
        catch (error) {
            console.error('Create employee error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to create employee');
        }
    }
    static async updateEmployee(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            delete updateData.password;
            delete updateData._id;
            delete updateData.createdAt;
            delete updateData.updatedAt;
            if (updateData.employeeCode) {
                const existingEmployee = await models_1.User.findOne({
                    employeeCode: updateData.employeeCode,
                    _id: { $ne: id }
                });
                if (existingEmployee) {
                    utils_1.ResponseUtils.conflict(res, 'Employee code already exists');
                    return;
                }
            }
            if (updateData.email) {
                const existingEmail = await models_1.User.findOne({
                    email: updateData.email,
                    _id: { $ne: id }
                });
                if (existingEmail) {
                    utils_1.ResponseUtils.conflict(res, 'Email already exists');
                    return;
                }
            }
            if (updateData.username) {
                const existingUsername = await models_1.User.findOne({
                    username: updateData.username,
                    _id: { $ne: id }
                });
                if (existingUsername) {
                    utils_1.ResponseUtils.conflict(res, 'Username already exists');
                    return;
                }
            }
            const employee = await models_1.User.findByIdAndUpdate(id, updateData, { new: true, runValidators: true }).select('-password');
            if (!employee) {
                utils_1.ResponseUtils.notFound(res, 'Employee not found');
                return;
            }
            utils_1.ResponseUtils.success(res, employee, 'Employee updated successfully');
        }
        catch (error) {
            console.error('Update employee error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to update employee');
        }
    }
    static async deleteEmployee(req, res) {
        try {
            const { id } = req.params;
            const employee = await models_1.User.findByIdAndUpdate(id, { isActive: false, employeeStatus: 'Đã nghỉ việc' }, { new: true }).select('-password');
            if (!employee) {
                utils_1.ResponseUtils.notFound(res, 'Employee not found');
                return;
            }
            utils_1.ResponseUtils.success(res, employee, 'Employee deactivated successfully');
        }
        catch (error) {
            console.error('Delete employee error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to deactivate employee');
        }
    }
    static async resetEmployeePassword(req, res) {
        try {
            const { id } = req.params;
            const { newPassword } = req.body;
            const employee = await models_1.User.findById(id);
            if (!employee) {
                utils_1.ResponseUtils.notFound(res, 'Employee not found');
                return;
            }
            employee.password = newPassword || 'Employee123!';
            await employee.save();
            utils_1.ResponseUtils.success(res, { message: 'Password reset successfully' }, 'Employee password reset successfully');
        }
        catch (error) {
            console.error('Reset employee password error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to reset employee password');
        }
    }
    static async getEmployeeStats(req, res) {
        try {
            const stats = await models_1.User.aggregate([
                {
                    $group: {
                        _id: null,
                        totalEmployees: { $sum: 1 },
                        activeEmployees: {
                            $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
                        },
                        inactiveEmployees: {
                            $sum: { $cond: [{ $eq: ['$isActive', false] }, 1, 0] }
                        }
                    }
                }
            ]);
            const departmentStats = await models_1.User.aggregate([
                { $match: { isActive: true } },
                {
                    $group: {
                        _id: '$department',
                        count: { $sum: 1 }
                    }
                },
                { $sort: { count: -1 } }
            ]);
            const genderStats = await models_1.User.aggregate([
                { $match: { isActive: true } },
                {
                    $group: {
                        _id: '$gender',
                        count: { $sum: 1 }
                    }
                }
            ]);
            const statusStats = await models_1.User.aggregate([
                {
                    $group: {
                        _id: '$employeeStatus',
                        count: { $sum: 1 }
                    }
                }
            ]);
            const result = {
                overview: stats[0] || { totalEmployees: 0, activeEmployees: 0, inactiveEmployees: 0 },
                byDepartment: departmentStats,
                byGender: genderStats,
                byStatus: statusStats
            };
            utils_1.ResponseUtils.success(res, result, 'Employee statistics retrieved successfully');
        }
        catch (error) {
            console.error('Get employee stats error:', error);
            utils_1.ResponseUtils.error(res, 'Failed to retrieve employee statistics');
        }
    }
}
exports.default = EmployeeController;
//# sourceMappingURL=employeeController.js.map