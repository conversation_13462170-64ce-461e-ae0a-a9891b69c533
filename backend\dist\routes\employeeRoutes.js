"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const employeeController_1 = __importDefault(require("@/controllers/employeeController"));
const validators_1 = require("@/validators");
const middleware_1 = require("@/middleware");
const types_1 = require("@/types");
const router = (0, express_1.Router)();
router.use(middleware_1.authenticate);
router.get('/', (0, middleware_1.authorize)(types_1.UserRole.ADMIN, types_1.UserRole.MANAGER), validators_1.employeeQueryValidation, middleware_1.validateRequest, employeeController_1.default.getAllEmployees);
router.get('/stats', (0, middleware_1.authorize)(types_1.UserRole.ADMIN, types_1.UserRole.MANAGER), employeeController_1.default.getEmployeeStats);
router.get('/code/:code', (0, middleware_1.authorize)(types_1.UserRole.ADMIN, types_1.UserRole.MANAGER), validators_1.getEmployeeByCodeValidation, middleware_1.validateRequest, employeeController_1.default.getEmployeeByCode);
router.get('/:id', (0, middleware_1.authorize)(types_1.UserRole.ADMIN, types_1.UserRole.MANAGER), validators_1.getEmployeeValidation, middleware_1.validateRequest, employeeController_1.default.getEmployeeById);
router.post('/', (0, middleware_1.authorize)(types_1.UserRole.ADMIN), validators_1.createEmployeeValidation, middleware_1.validateRequest, employeeController_1.default.createEmployee);
router.put('/:id', (0, middleware_1.authorize)(types_1.UserRole.ADMIN), validators_1.updateEmployeeValidation, middleware_1.validateRequest, employeeController_1.default.updateEmployee);
router.delete('/:id', (0, middleware_1.authorize)(types_1.UserRole.ADMIN), validators_1.getEmployeeValidation, middleware_1.validateRequest, employeeController_1.default.deleteEmployee);
router.post('/:id/reset-password', (0, middleware_1.authorize)(types_1.UserRole.ADMIN), validators_1.resetPasswordValidation, middleware_1.validateRequest, employeeController_1.default.resetEmployeePassword);
exports.default = router;
//# sourceMappingURL=employeeRoutes.js.map