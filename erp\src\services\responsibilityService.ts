import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api/v1';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {
            refreshToken
          });
          
          const { accessToken } = response.data.data;
          localStorage.setItem('accessToken', accessToken);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

export interface ResponsibilityItem {
  _id?: string;
  description: string;
  weight: number;
}

export interface Responsibility {
  _id: string;
  responsibilityCode: string;
  employeeName: string;
  currentlyDisplayed: boolean;
  position: string;
  responsibilities: ResponsibilityItem[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateResponsibilityRequest {
  employeeName: string;
  currentlyDisplayed: boolean;
  position: string;
  responsibilities: ResponsibilityItem[];
}

export interface UpdateResponsibilityRequest {
  employeeName?: string;
  currentlyDisplayed?: boolean;
  position?: string;
  responsibilities?: ResponsibilityItem[];
}

export interface ResponsibilityQueryParams {
  page?: number;
  limit?: number;
  employeeName?: string;
  position?: string;
  currentlyDisplayed?: boolean;
}

export interface ResponsibilityResponse {
  responsibilities: Responsibility[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

export interface ResponsibilityStats {
  totalResponsibilities: number;
  displayedResponsibilities: number;
  hiddenResponsibilities: number;
  positionBreakdown: Array<{
    _id: string;
    count: number;
  }>;
}

class ResponsibilityService {
  // Get all responsibilities with pagination and filtering
  static async getAllResponsibilities(params?: ResponsibilityQueryParams): Promise<ResponsibilityResponse> {
    try {
      const response = await api.get('/responsibilities', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching responsibilities:', error);
      throw error;
    }
  }

  // Get responsibility by ID
  static async getResponsibilityById(id: string): Promise<Responsibility> {
    try {
      const response = await api.get(`/responsibilities/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching responsibility:', error);
      throw error;
    }
  }

  // Get responsibility by code
  static async getResponsibilityByCode(code: string): Promise<Responsibility> {
    try {
      const response = await api.get(`/responsibilities/code/${code}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching responsibility by code:', error);
      throw error;
    }
  }

  // Create new responsibility
  static async createResponsibility(data: CreateResponsibilityRequest): Promise<Responsibility> {
    try {
      const response = await api.post('/responsibilities', data);
      return response.data.data;
    } catch (error) {
      console.error('Error creating responsibility:', error);
      throw error;
    }
  }

  // Update responsibility
  static async updateResponsibility(id: string, data: UpdateResponsibilityRequest): Promise<Responsibility> {
    try {
      const response = await api.put(`/responsibilities/${id}`, data);
      return response.data.data;
    } catch (error) {
      console.error('Error updating responsibility:', error);
      throw error;
    }
  }

  // Delete responsibility
  static async deleteResponsibility(id: string): Promise<void> {
    try {
      await api.delete(`/responsibilities/${id}`);
    } catch (error) {
      console.error('Error deleting responsibility:', error);
      throw error;
    }
  }

  // Get responsibility statistics
  static async getResponsibilityStats(): Promise<ResponsibilityStats> {
    try {
      const response = await api.get('/responsibilities/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching responsibility stats:', error);
      throw error;
    }
  }
}

export default ResponsibilityService;
