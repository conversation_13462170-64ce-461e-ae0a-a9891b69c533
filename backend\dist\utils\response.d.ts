import { Response } from 'express';
declare class ResponseUtils {
    static success<T>(res: Response, data?: T, message?: string, statusCode?: number): Response;
    static error(res: Response, message?: string, statusCode?: number, error?: string): Response;
    static paginated<T>(res: Response, data: T[], currentPage: number, totalItems: number, itemsPerPage: number, message?: string): Response;
    static created<T>(res: Response, data?: T, message?: string): Response;
    static noContent(res: Response): Response;
    static badRequest(res: Response, message?: string, error?: string): Response;
    static unauthorized(res: Response, message?: string): Response;
    static forbidden(res: Response, message?: string): Response;
    static notFound(res: Response, message?: string): Response;
    static conflict(res: Response, message?: string, error?: string): Response;
    static validationError(res: Response, errors: any, message?: string): Response;
}
export default ResponseUtils;
//# sourceMappingURL=response.d.ts.map