import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest, UserRole } from '@/types';
import { JWTUtils, ResponseUtils } from '@/utils';
import { User } from '@/models';

/**
 * Middleware to authenticate user using JWT token
 */
export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      ResponseUtils.unauthorized(res, 'Access token is required');
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token
    const decoded = JWTUtils.verifyAccessToken(token);
    
    // Get user from database
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      ResponseUtils.unauthorized(res, 'User not found');
      return;
    }

    if (!user.isActive) {
      ResponseUtils.unauthorized(res, 'Account is deactivated');
      return;
    }

    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    ResponseUtils.unauthorized(res, 'Invalid or expired token');
  }
};

/**
 * Middleware to authorize user based on roles
 */
export const authorize = (...roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      ResponseUtils.unauthorized(res, 'Authentication required');
      return;
    }

    if (!roles.includes(req.user.role as UserRole)) {
      ResponseUtils.forbidden(res, 'Insufficient permissions');
      return;
    }

    next();
  };
};

/**
 * Middleware to check if user is admin
 */
export const requireAdmin = authorize(UserRole.ADMIN);

/**
 * Middleware to check if user is admin or manager
 */
export const requireManagerOrAdmin = authorize(UserRole.ADMIN, UserRole.MANAGER);

/**
 * Optional authentication middleware (doesn't fail if no token)
 */
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const decoded = JWTUtils.verifyAccessToken(token);
      const user = await User.findById(decoded.userId);
      
      if (user && user.isActive) {
        req.user = user;
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication
    next();
  }
};
