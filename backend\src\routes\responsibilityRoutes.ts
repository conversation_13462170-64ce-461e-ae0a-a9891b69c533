import { Router } from 'express';
import ResponsibilityController from '@/controllers/responsibilityController';
import { 
  createResponsibilityValidation,
  updateResponsibilityValidation,
  getResponsibilityValidation,
  getResponsibilityByCodeValidation,
  responsibilityQueryValidation
} from '@/validators/responsibilityValidators';
import { authenticate, authorize, validateRequest } from '@/middleware';
import { UserRole } from '@/types';

const router = Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @route   GET /api/v1/responsibilities
 * @desc    Get all responsibilities with pagination and filtering
 * @access  Admin, Manager
 */
router.get(
  '/',
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  responsibilityQueryValidation,
  validateRequest,
  ResponsibilityController.getAllResponsibilities
);

/**
 * @route   GET /api/v1/responsibilities/stats
 * @desc    Get responsibility statistics
 * @access  Admin, Manager
 */
router.get(
  '/stats',
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  ResponsibilityController.getResponsibilityStats
);

/**
 * @route   GET /api/v1/responsibilities/code/:code
 * @desc    Get responsibility by responsibility code
 * @access  Admin, Manager
 */
router.get(
  '/code/:code',
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  getResponsibilityByCodeValidation,
  validateRequest,
  ResponsibilityController.getResponsibilityByCode
);

/**
 * @route   GET /api/v1/responsibilities/:id
 * @desc    Get responsibility by ID
 * @access  Admin, Manager
 */
router.get(
  '/:id',
  authorize(UserRole.ADMIN, UserRole.MANAGER),
  getResponsibilityValidation,
  validateRequest,
  ResponsibilityController.getResponsibilityById
);

/**
 * @route   POST /api/v1/responsibilities
 * @desc    Create new responsibility
 * @access  Admin only
 */
router.post(
  '/',
  authorize(UserRole.ADMIN),
  createResponsibilityValidation,
  validateRequest,
  ResponsibilityController.createResponsibility
);

/**
 * @route   PUT /api/v1/responsibilities/:id
 * @desc    Update responsibility
 * @access  Admin only
 */
router.put(
  '/:id',
  authorize(UserRole.ADMIN),
  updateResponsibilityValidation,
  validateRequest,
  ResponsibilityController.updateResponsibility
);

/**
 * @route   DELETE /api/v1/responsibilities/:id
 * @desc    Delete responsibility
 * @access  Admin only
 */
router.delete(
  '/:id',
  authorize(UserRole.ADMIN),
  getResponsibilityValidation,
  validateRequest,
  ResponsibilityController.deleteResponsibility
);

export default router;
