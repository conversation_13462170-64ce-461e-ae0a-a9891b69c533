"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const crypto_1 = __importDefault(require("crypto"));
const config_1 = require("@/config/config");
const models_1 = require("@/models");
class JWTUtils {
    static generateAccessToken(user) {
        const payload = {
            userId: user._id,
            email: user.email,
            role: user.role
        };
        const options = {
            expiresIn: config_1.config.jwt.expiresIn,
            issuer: 'erp-system',
            audience: 'erp-users'
        };
        return jsonwebtoken_1.default.sign(payload, config_1.config.jwt.secret, options);
    }
    static async generateRefreshToken(userId) {
        const token = crypto_1.default.randomBytes(64).toString('hex');
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 30);
        await models_1.RefreshToken.create({
            token,
            userId,
            expiresAt
        });
        return token;
    }
    static verifyAccessToken(token) {
        try {
            const options = {
                issuer: 'erp-system',
                audience: 'erp-users'
            };
            return jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret, options);
        }
        catch (error) {
            throw new Error('Invalid or expired access token');
        }
    }
    static async verifyRefreshToken(token) {
        const refreshToken = await models_1.RefreshToken.findOne({
            token,
            isRevoked: false,
            expiresAt: { $gt: new Date() }
        });
        if (!refreshToken) {
            throw new Error('Invalid or expired refresh token');
        }
        return refreshToken.userId;
    }
    static async revokeRefreshToken(token) {
        await models_1.RefreshToken.updateOne({ token }, { isRevoked: true });
    }
    static async revokeAllUserTokens(userId) {
        await models_1.RefreshToken.updateMany({ userId }, { isRevoked: true });
    }
    static async cleanupExpiredTokens() {
        await models_1.RefreshToken.deleteMany({
            $or: [
                { expiresAt: { $lt: new Date() } },
                { isRevoked: true }
            ]
        });
    }
    static async generateTokenPair(user) {
        const accessToken = this.generateAccessToken(user);
        const refreshToken = await this.generateRefreshToken(user._id);
        return { accessToken, refreshToken };
    }
}
exports.default = JWTUtils;
//# sourceMappingURL=jwt.js.map