export declare const createResponsibilityValidation: import("express-validator").ValidationChain[];
export declare const updateResponsibilityValidation: import("express-validator").ValidationChain[];
export declare const getResponsibilityValidation: import("express-validator").ValidationChain[];
export declare const getResponsibilityByCodeValidation: import("express-validator").ValidationChain[];
export declare const responsibilityQueryValidation: import("express-validator").ValidationChain[];
//# sourceMappingURL=responsibilityValidators.d.ts.map