import jwt, { SignOptions } from 'jsonwebtoken';
import crypto from 'crypto';
import { StringValue } from 'ms';
import { config } from '@/config/config';
import { IUser } from '@/types';
import { RefreshToken } from '@/models';

interface TokenPayload {
  userId: string;
  email: string;
  role: string;
}

class JWTUtils {
  /**
   * Generate access token
   */
  static generateAccessToken(user: IUser): string {
    const payload: TokenPayload = {
      userId: user._id,
      email: user.email,
      role: user.role
    };

    const options: SignOptions = {
      expiresIn: config.jwt.expiresIn as StringValue,
      issuer: 'erp-system',
      audience: 'erp-users'
    };

    return jwt.sign(payload, config.jwt.secret, options);
  }

  /**
   * Generate refresh token
   */
  static async generateRefreshToken(userId: string): Promise<string> {
    const token = crypto.randomBytes(64).toString('hex');
    
    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30); // 30 days

    // Save refresh token to database
    await RefreshToken.create({
      token,
      userId,
      expiresAt
    });

    return token;
  }

  /**
   * Verify access token
   */
  static verifyAccessToken(token: string): TokenPayload {
    try {
      const options = {
        issuer: 'erp-system',
        audience: 'erp-users'
      };

      return jwt.verify(token, config.jwt.secret, options) as TokenPayload;
    } catch (error) {
      throw new Error('Invalid or expired access token');
    }
  }

  /**
   * Verify refresh token
   */
  static async verifyRefreshToken(token: string): Promise<string> {
    const refreshToken = await RefreshToken.findOne({
      token,
      isRevoked: false,
      expiresAt: { $gt: new Date() }
    });

    if (!refreshToken) {
      throw new Error('Invalid or expired refresh token');
    }

    return refreshToken.userId;
  }

  /**
   * Revoke refresh token
   */
  static async revokeRefreshToken(token: string): Promise<void> {
    await RefreshToken.updateOne(
      { token },
      { isRevoked: true }
    );
  }

  /**
   * Revoke all refresh tokens for a user
   */
  static async revokeAllUserTokens(userId: string): Promise<void> {
    await RefreshToken.updateMany(
      { userId },
      { isRevoked: true }
    );
  }

  /**
   * Clean up expired tokens
   */
  static async cleanupExpiredTokens(): Promise<void> {
    await RefreshToken.deleteMany({
      $or: [
        { expiresAt: { $lt: new Date() } },
        { isRevoked: true }
      ]
    });
  }

  /**
   * Generate token pair (access + refresh)
   */
  static async generateTokenPair(user: IUser): Promise<{ accessToken: string; refreshToken: string }> {
    const accessToken = this.generateAccessToken(user);
    const refreshToken = await this.generateRefreshToken(user._id);

    return { accessToken, refreshToken };
  }
}

export default JWTUtils;
