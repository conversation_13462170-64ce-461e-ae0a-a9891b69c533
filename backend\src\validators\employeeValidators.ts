import { body, param, query } from 'express-validator';
import { UserRole } from '@/types';

export const createEmployeeValidation = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_\u00C0-\u017F\u1EA0-\u1EF9]+$/)
    .withMessage('Username can only contain letters, numbers, underscores, and Vietnamese characters'),

  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),

  body('password')
    .optional()
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),

  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required and must not exceed 50 characters')
    .matches(/^[a-zA-ZÀ-ÿ\u00C0-\u017F\u1EA0-\u1EF9\s]+$/)
    .withMessage('First name can only contain letters, spaces, and Vietnamese characters'),

  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required and must not exceed 50 characters')
    .matches(/^[a-zA-ZÀ-ÿ\u00C0-\u017F\u1EA0-\u1EF9\s]+$/)
    .withMessage('Last name can only contain letters, spaces, and Vietnamese characters'),

  body('role')
    .optional()
    .isIn(Object.values(UserRole))
    .withMessage('Invalid role specified'),

  body('department')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Department must not exceed 100 characters'),

  body('position')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Position must not exceed 100 characters'),

  // Employee specific validations
  body('employeeCode')
    .optional()
    .trim()
    .isLength({ max: 20 })
    .withMessage('Employee code must not exceed 20 characters')
    .matches(/^[a-zA-Z0-9\-_]+$/)
    .withMessage('Employee code can only contain letters, numbers, hyphens, and underscores'),

  body('gender')
    .optional()
    .isIn(['Nam', 'Nữ', 'Khác'])
    .withMessage('Gender must be Nam, Nữ, or Khác'),

  body('subDepartment')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Sub-department must not exceed 100 characters'),

  body('weight')
    .optional()
    .isFloat({ min: 0, max: 500 })
    .withMessage('Weight must be between 0 and 500 kg'),

  body('height')
    .optional()
    .isFloat({ min: 0, max: 300 })
    .withMessage('Height must be between 0 and 300 cm'),

  body('shirtSize')
    .optional()
    .isIn(['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'])
    .withMessage('Shirt size must be XS, S, M, L, XL, XXL, or XXXL'),

  body('pantSize')
    .optional()
    .trim()
    .isLength({ max: 10 })
    .withMessage('Pant size must not exceed 10 characters'),

  body('shoeSize')
    .optional()
    .trim()
    .isLength({ max: 10 })
    .withMessage('Shoe size must not exceed 10 characters'),

  body('personalPhone')
    .optional()
    .trim()
    .matches(/^[0-9+\-\s()]+$/)
    .withMessage('Please enter a valid phone number')
    .isLength({ max: 20 })
    .withMessage('Phone number must not exceed 20 characters'),

  body('bankAccount')
    .optional()
    .trim()
    .isLength({ max: 30 })
    .withMessage('Bank account must not exceed 30 characters'),

  body('lockerNumber')
    .optional()
    .trim()
    .isLength({ max: 10 })
    .withMessage('Locker number must not exceed 10 characters'),

  body('employeeStatus')
    .optional()
    .isIn(['Đang làm việc', 'Nghỉ phép', 'Tạm nghỉ', 'Đã nghỉ việc', 'Thử việc'])
    .withMessage('Invalid employee status'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean value')
];

export const updateEmployeeValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid employee ID'),

  body('username')
    .optional()
    .trim()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_\u00C0-\u017F\u1EA0-\u1EF9]+$/)
    .withMessage('Username can only contain letters, numbers, underscores, and Vietnamese characters'),

  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),

  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must not exceed 50 characters')
    .matches(/^[a-zA-ZÀ-ÿ\u00C0-\u017F\u1EA0-\u1EF9\s]+$/)
    .withMessage('First name can only contain letters, spaces, and Vietnamese characters'),

  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must not exceed 50 characters')
    .matches(/^[a-zA-ZÀ-ÿ\u00C0-\u017F\u1EA0-\u1EF9\s]+$/)
    .withMessage('Last name can only contain letters, spaces, and Vietnamese characters'),

  body('role')
    .optional()
    .isIn(Object.values(UserRole))
    .withMessage('Invalid role specified'),

  body('department')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Department must not exceed 100 characters'),

  body('position')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Position must not exceed 100 characters'),

  // Employee specific validations
  body('employeeCode')
    .optional()
    .trim()
    .isLength({ max: 20 })
    .withMessage('Employee code must not exceed 20 characters')
    .matches(/^[a-zA-Z0-9\-_]+$/)
    .withMessage('Employee code can only contain letters, numbers, hyphens, and underscores'),

  body('gender')
    .optional()
    .isIn(['Nam', 'Nữ', 'Khác'])
    .withMessage('Gender must be Nam, Nữ, or Khác'),

  body('subDepartment')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Sub-department must not exceed 100 characters'),

  body('weight')
    .optional()
    .isFloat({ min: 0, max: 500 })
    .withMessage('Weight must be between 0 and 500 kg'),

  body('height')
    .optional()
    .isFloat({ min: 0, max: 300 })
    .withMessage('Height must be between 0 and 300 cm'),

  body('shirtSize')
    .optional()
    .isIn(['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'])
    .withMessage('Shirt size must be XS, S, M, L, XL, XXL, or XXXL'),

  body('pantSize')
    .optional()
    .trim()
    .isLength({ max: 10 })
    .withMessage('Pant size must not exceed 10 characters'),

  body('shoeSize')
    .optional()
    .trim()
    .isLength({ max: 10 })
    .withMessage('Shoe size must not exceed 10 characters'),

  body('personalPhone')
    .optional()
    .trim()
    .matches(/^[0-9+\-\s()]+$/)
    .withMessage('Please enter a valid phone number')
    .isLength({ max: 20 })
    .withMessage('Phone number must not exceed 20 characters'),

  body('bankAccount')
    .optional()
    .trim()
    .isLength({ max: 30 })
    .withMessage('Bank account must not exceed 30 characters'),

  body('lockerNumber')
    .optional()
    .trim()
    .isLength({ max: 10 })
    .withMessage('Locker number must not exceed 10 characters'),

  body('employeeStatus')
    .optional()
    .isIn(['Đang làm việc', 'Nghỉ phép', 'Tạm nghỉ', 'Đã nghỉ việc', 'Thử việc'])
    .withMessage('Invalid employee status'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean value')
];

export const getEmployeeValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid employee ID')
];

export const getEmployeeByCodeValidation = [
  param('code')
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Employee code must be between 1 and 20 characters')
];

export const resetPasswordValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid employee ID'),

  body('newPassword')
    .optional()
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
];

export const employeeQueryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('department')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Department filter must not exceed 100 characters'),

  query('subDepartment')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Sub-department filter must not exceed 100 characters'),

  query('gender')
    .optional()
    .isIn(['Nam', 'Nữ', 'Khác'])
    .withMessage('Gender filter must be Nam, Nữ, or Khác'),

  query('employeeStatus')
    .optional()
    .isIn(['Đang làm việc', 'Nghỉ phép', 'Tạm nghỉ', 'Đã nghỉ việc', 'Thử việc'])
    .withMessage('Invalid employee status filter'),

  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive filter must be a boolean value'),

  query('search')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Search term must not exceed 100 characters')
];
