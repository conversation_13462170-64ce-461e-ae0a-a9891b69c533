export declare const createEmployeeValidation: import("express-validator").ValidationChain[];
export declare const updateEmployeeValidation: import("express-validator").ValidationChain[];
export declare const getEmployeeValidation: import("express-validator").ValidationChain[];
export declare const getEmployeeByCodeValidation: import("express-validator").ValidationChain[];
export declare const resetPasswordValidation: import("express-validator").ValidationChain[];
export declare const employeeQueryValidation: import("express-validator").ValidationChain[];
//# sourceMappingURL=employeeValidators.d.ts.map