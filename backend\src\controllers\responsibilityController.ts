import { Request, Response } from 'express';
import Responsibility from '@/models/Responsibility';
import { IResponsibility } from '@/types';
import { ApiResponse } from '@/utils/response';

class ResponsibilityController {
  /**
   * Get all responsibilities with pagination and filtering
   */
  static async getAllResponsibilities(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      // Build filter object
      const filter: any = {};
      
      if (req.query.employeeName) {
        filter.employeeName = { $regex: req.query.employeeName, $options: 'i' };
      }
      
      if (req.query.position) {
        filter.position = { $regex: req.query.position, $options: 'i' };
      }
      
      if (req.query.currentlyDisplayed !== undefined) {
        filter.currentlyDisplayed = req.query.currentlyDisplayed === 'true';
      }

      // Get responsibilities with pagination
      const [responsibilities, total] = await Promise.all([
        Responsibility.find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Responsibility.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limit);

      res.json(ApiResponse.success({
        responsibilities,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: total,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }, 'Responsibilities retrieved successfully'));
    } catch (error) {
      console.error('Error getting responsibilities:', error);
      res.status(500).json(ApiResponse.error('Failed to retrieve responsibilities'));
    }
  }

  /**
   * Get responsibility by ID
   */
  static async getResponsibilityById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const responsibility = await Responsibility.findById(id).lean();
      
      if (!responsibility) {
        res.status(404).json(ApiResponse.error('Responsibility not found'));
        return;
      }

      res.json(ApiResponse.success(responsibility, 'Responsibility retrieved successfully'));
    } catch (error) {
      console.error('Error getting responsibility:', error);
      res.status(500).json(ApiResponse.error('Failed to retrieve responsibility'));
    }
  }

  /**
   * Get responsibility by code
   */
  static async getResponsibilityByCode(req: Request, res: Response): Promise<void> {
    try {
      const { code } = req.params;
      
      const responsibility = await Responsibility.findOne({ responsibilityCode: code }).lean();
      
      if (!responsibility) {
        res.status(404).json(ApiResponse.error('Responsibility not found'));
        return;
      }

      res.json(ApiResponse.success(responsibility, 'Responsibility retrieved successfully'));
    } catch (error) {
      console.error('Error getting responsibility by code:', error);
      res.status(500).json(ApiResponse.error('Failed to retrieve responsibility'));
    }
  }

  /**
   * Create new responsibility
   */
  static async createResponsibility(req: Request, res: Response): Promise<void> {
    try {
      const { employeeName, currentlyDisplayed, position, responsibilities } = req.body;

      // Generate next responsibility code
      const responsibilityCode = await Responsibility.generateNextCode();

      // Validate total weight
      const totalWeight = responsibilities.reduce((sum: number, item: any) => sum + item.weight, 0);
      if (totalWeight > 100) {
        res.status(400).json(ApiResponse.error('Total weight of responsibilities cannot exceed 100%'));
        return;
      }

      const newResponsibility = new Responsibility({
        responsibilityCode,
        employeeName,
        currentlyDisplayed,
        position,
        responsibilities
      });

      const savedResponsibility = await newResponsibility.save();

      res.status(201).json(ApiResponse.success(
        savedResponsibility,
        'Responsibility created successfully'
      ));
    } catch (error: any) {
      console.error('Error creating responsibility:', error);
      
      if (error.name === 'ValidationError') {
        const validationErrors = Object.values(error.errors).map((err: any) => err.message);
        res.status(400).json(ApiResponse.error('Validation failed', validationErrors));
        return;
      }
      
      if (error.code === 11000) {
        res.status(400).json(ApiResponse.error('Responsibility code already exists'));
        return;
      }

      res.status(500).json(ApiResponse.error('Failed to create responsibility'));
    }
  }

  /**
   * Update responsibility
   */
  static async updateResponsibility(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { employeeName, currentlyDisplayed, position, responsibilities } = req.body;

      // Validate total weight
      if (responsibilities) {
        const totalWeight = responsibilities.reduce((sum: number, item: any) => sum + item.weight, 0);
        if (totalWeight > 100) {
          res.status(400).json(ApiResponse.error('Total weight of responsibilities cannot exceed 100%'));
          return;
        }
      }

      const updatedResponsibility = await Responsibility.findByIdAndUpdate(
        id,
        {
          employeeName,
          currentlyDisplayed,
          position,
          responsibilities
        },
        { new: true, runValidators: true }
      );

      if (!updatedResponsibility) {
        res.status(404).json(ApiResponse.error('Responsibility not found'));
        return;
      }

      res.json(ApiResponse.success(
        updatedResponsibility,
        'Responsibility updated successfully'
      ));
    } catch (error: any) {
      console.error('Error updating responsibility:', error);
      
      if (error.name === 'ValidationError') {
        const validationErrors = Object.values(error.errors).map((err: any) => err.message);
        res.status(400).json(ApiResponse.error('Validation failed', validationErrors));
        return;
      }

      res.status(500).json(ApiResponse.error('Failed to update responsibility'));
    }
  }

  /**
   * Delete responsibility
   */
  static async deleteResponsibility(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const deletedResponsibility = await Responsibility.findByIdAndDelete(id);
      
      if (!deletedResponsibility) {
        res.status(404).json(ApiResponse.error('Responsibility not found'));
        return;
      }

      res.json(ApiResponse.success(
        null,
        'Responsibility deleted successfully'
      ));
    } catch (error) {
      console.error('Error deleting responsibility:', error);
      res.status(500).json(ApiResponse.error('Failed to delete responsibility'));
    }
  }

  /**
   * Get responsibility statistics
   */
  static async getResponsibilityStats(req: Request, res: Response): Promise<void> {
    try {
      const [totalCount, displayedCount, positionStats] = await Promise.all([
        Responsibility.countDocuments(),
        Responsibility.countDocuments({ currentlyDisplayed: true }),
        Responsibility.aggregate([
          {
            $group: {
              _id: '$position',
              count: { $sum: 1 }
            }
          },
          { $sort: { count: -1 } }
        ])
      ]);

      const stats = {
        totalResponsibilities: totalCount,
        displayedResponsibilities: displayedCount,
        hiddenResponsibilities: totalCount - displayedCount,
        positionBreakdown: positionStats
      };

      res.json(ApiResponse.success(stats, 'Responsibility statistics retrieved successfully'));
    } catch (error) {
      console.error('Error getting responsibility stats:', error);
      res.status(500).json(ApiResponse.error('Failed to retrieve responsibility statistics'));
    }
  }
}

export default ResponsibilityController;
