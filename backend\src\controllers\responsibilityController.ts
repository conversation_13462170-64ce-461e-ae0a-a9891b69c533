import { Request, Response } from 'express';
import Responsibility from '@/models/Responsibility';

class ResponsibilityController {
  /**
   * Get all responsibilities with pagination and filtering
   */
  static async getAllResponsibilities(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      // Build filter object
      const filter: any = {};

      if (req.query.employeeName) {
        filter.employeeName = { $regex: req.query.employeeName, $options: 'i' };
      }

      if (req.query.position) {
        filter.position = { $regex: req.query.position, $options: 'i' };
      }

      if (req.query.currentlyDisplayed !== undefined) {
        filter.currentlyDisplayed = req.query.currentlyDisplayed === 'true';
      }

      // Get responsibilities with pagination
      const [responsibilities, total] = await Promise.all([
        Responsibility.find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Responsibility.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        message: 'Responsibilities retrieved successfully',
        data: {
          responsibilities,
          pagination: {
            currentPage: page,
            totalPages,
            totalItems: total,
            itemsPerPage: limit,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error getting responsibilities:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve responsibilities',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Create new responsibility
   */
  static async createResponsibility(req: Request, res: Response): Promise<void> {
    try {
      const { employeeName, currentlyDisplayed, position, responsibilities } = req.body;

      // Generate next responsibility code
      const responsibilityCode = await Responsibility.generateNextCode();

      // Validate total weight
      const totalWeight = responsibilities.reduce((sum: number, item: any) => sum + item.weight, 0);
      if (totalWeight > 100) {
        res.status(400).json({
          success: false,
          message: 'Total weight of responsibilities cannot exceed 100%',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const newResponsibility = new Responsibility({
        responsibilityCode,
        employeeName,
        currentlyDisplayed,
        position,
        responsibilities
      });

      const savedResponsibility = await newResponsibility.save();

      res.status(201).json({
        success: true,
        message: 'Responsibility created successfully',
        data: savedResponsibility,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      console.error('Error creating responsibility:', error);

      if (error.name === 'ValidationError') {
        const validationErrors = Object.values(error.errors).map((err: any) => err.message);
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: validationErrors,
          timestamp: new Date().toISOString()
        });
        return;
      }

      if (error.code === 11000) {
        res.status(400).json({
          success: false,
          message: 'Responsibility code already exists',
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to create responsibility',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get responsibility by ID
   */
  static async getResponsibilityById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const responsibility = await Responsibility.findById(id).lean();

      if (!responsibility) {
        res.status(404).json({
          success: false,
          message: 'Responsibility not found',
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.json({
        success: true,
        message: 'Responsibility retrieved successfully',
        data: responsibility,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error getting responsibility:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve responsibility',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get responsibility by code
   */
  static async getResponsibilityByCode(req: Request, res: Response): Promise<void> {
    try {
      const { code } = req.params;

      const responsibility = await Responsibility.findOne({ responsibilityCode: code }).lean();

      if (!responsibility) {
        res.status(404).json({
          success: false,
          message: 'Responsibility not found',
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.json({
        success: true,
        message: 'Responsibility retrieved successfully',
        data: responsibility,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error getting responsibility by code:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve responsibility',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Update responsibility
   */
  static async updateResponsibility(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { employeeName, currentlyDisplayed, position, responsibilities } = req.body;

      // Validate total weight
      if (responsibilities) {
        const totalWeight = responsibilities.reduce((sum: number, item: any) => sum + item.weight, 0);
        if (totalWeight > 100) {
          res.status(400).json({
            success: false,
            message: 'Total weight of responsibilities cannot exceed 100%',
            timestamp: new Date().toISOString()
          });
          return;
        }
      }

      const updatedResponsibility = await Responsibility.findByIdAndUpdate(
        id,
        {
          employeeName,
          currentlyDisplayed,
          position,
          responsibilities
        },
        { new: true, runValidators: true }
      );

      if (!updatedResponsibility) {
        res.status(404).json({
          success: false,
          message: 'Responsibility not found',
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.json({
        success: true,
        message: 'Responsibility updated successfully',
        data: updatedResponsibility,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      console.error('Error updating responsibility:', error);

      if (error.name === 'ValidationError') {
        const validationErrors = Object.values(error.errors).map((err: any) => err.message);
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: validationErrors,
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to update responsibility',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Delete responsibility
   */
  static async deleteResponsibility(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const deletedResponsibility = await Responsibility.findByIdAndDelete(id);

      if (!deletedResponsibility) {
        res.status(404).json({
          success: false,
          message: 'Responsibility not found',
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.json({
        success: true,
        message: 'Responsibility deleted successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error deleting responsibility:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete responsibility',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get responsibility statistics
   */
  static async getResponsibilityStats(req: Request, res: Response): Promise<void> {
    try {
      const [totalCount, displayedCount, positionStats] = await Promise.all([
        Responsibility.countDocuments(),
        Responsibility.countDocuments({ currentlyDisplayed: true }),
        Responsibility.aggregate([
          {
            $group: {
              _id: '$position',
              count: { $sum: 1 }
            }
          },
          { $sort: { count: -1 } }
        ])
      ]);

      const stats = {
        totalResponsibilities: totalCount,
        displayedResponsibilities: displayedCount,
        hiddenResponsibilities: totalCount - displayedCount,
        positionBreakdown: positionStats
      };

      res.json({
        success: true,
        message: 'Responsibility statistics retrieved successfully',
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error getting responsibility stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve responsibility statistics',
        timestamp: new Date().toISOString()
      });
    }
  }
}

export default ResponsibilityController;
