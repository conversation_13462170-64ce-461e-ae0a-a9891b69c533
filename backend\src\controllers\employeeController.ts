import { Request, Response } from 'express';
import { User } from '@/models';
import { ResponseUtils } from '@/utils';
import { AuthenticatedRequest } from '@/types';

class EmployeeController {
  /**
   * Get all employees with pagination and filtering
   */
  static async getAllEmployees(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      // Build filter object
      const filter: any = {};
      
      if (req.query.department) {
        filter.department = new RegExp(req.query.department as string, 'i');
      }
      
      if (req.query.subDepartment) {
        filter.subDepartment = new RegExp(req.query.subDepartment as string, 'i');
      }
      
      if (req.query.gender) {
        filter.gender = req.query.gender;
      }
      
      if (req.query.employeeStatus) {
        filter.employeeStatus = req.query.employeeStatus;
      }
      
      if (req.query.isActive !== undefined) {
        filter.isActive = req.query.isActive === 'true';
      }

      // Search by name, email, or employee code
      if (req.query.search) {
        const searchRegex = new RegExp(req.query.search as string, 'i');
        filter.$or = [
          { firstName: searchRegex },
          { lastName: searchRegex },
          { email: searchRegex },
          { employeeCode: searchRegex },
          { username: searchRegex }
        ];
      }

      const employees = await User.find(filter)
        .select('-password')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const totalEmployees = await User.countDocuments(filter);

      ResponseUtils.paginated(res, employees, page, totalEmployees, limit, 'Employees retrieved successfully');

    } catch (error) {
      console.error('Get all employees error:', error);
      ResponseUtils.error(res, 'Failed to retrieve employees');
    }
  }

  /**
   * Get employee by ID
   */
  static async getEmployeeById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const employee = await User.findById(id).select('-password');

      if (!employee) {
        ResponseUtils.notFound(res, 'Employee not found');
        return;
      }

      ResponseUtils.success(res, employee, 'Employee retrieved successfully');

    } catch (error) {
      console.error('Get employee by ID error:', error);
      ResponseUtils.error(res, 'Failed to retrieve employee');
    }
  }

  /**
   * Get employee by employee code
   */
  static async getEmployeeByCode(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { code } = req.params;

      const employee = await User.findOne({ employeeCode: code }).select('-password');

      if (!employee) {
        ResponseUtils.notFound(res, 'Employee not found');
        return;
      }

      ResponseUtils.success(res, employee, 'Employee retrieved successfully');

    } catch (error) {
      console.error('Get employee by code error:', error);
      ResponseUtils.error(res, 'Failed to retrieve employee');
    }
  }

  /**
   * Create new employee
   */
  static async createEmployee(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const employeeData = req.body;

      // Check if employee code already exists
      if (employeeData.employeeCode) {
        const existingEmployee = await User.findOne({ employeeCode: employeeData.employeeCode });
        if (existingEmployee) {
          ResponseUtils.conflict(res, 'Employee code already exists');
          return;
        }
      }

      // Check if email already exists
      const existingEmail = await User.findOne({ email: employeeData.email });
      if (existingEmail) {
        ResponseUtils.conflict(res, 'Email already exists');
        return;
      }

      // Check if username already exists
      const existingUsername = await User.findOne({ username: employeeData.username });
      if (existingUsername) {
        ResponseUtils.conflict(res, 'Username already exists');
        return;
      }

      // Set default password if not provided
      if (!employeeData.password) {
        employeeData.password = 'Employee123!'; // Default password
      }

      // Create new employee
      const employee = new User(employeeData);
      await employee.save();

      // Remove password from response
      const employeeResponse = employee.toJSON();

      ResponseUtils.created(res, employeeResponse, 'Employee created successfully');

    } catch (error) {
      console.error('Create employee error:', error);
      ResponseUtils.error(res, 'Failed to create employee');
    }
  }

  /**
   * Update employee
   */
  static async updateEmployee(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Remove sensitive fields that shouldn't be updated via this endpoint
      delete updateData.password;
      delete updateData._id;
      delete updateData.createdAt;
      delete updateData.updatedAt;

      // Check if employee code is being changed and if it already exists
      if (updateData.employeeCode) {
        const existingEmployee = await User.findOne({ 
          employeeCode: updateData.employeeCode,
          _id: { $ne: id }
        });
        if (existingEmployee) {
          ResponseUtils.conflict(res, 'Employee code already exists');
          return;
        }
      }

      // Check if email is being changed and if it already exists
      if (updateData.email) {
        const existingEmail = await User.findOne({ 
          email: updateData.email,
          _id: { $ne: id }
        });
        if (existingEmail) {
          ResponseUtils.conflict(res, 'Email already exists');
          return;
        }
      }

      // Check if username is being changed and if it already exists
      if (updateData.username) {
        const existingUsername = await User.findOne({ 
          username: updateData.username,
          _id: { $ne: id }
        });
        if (existingUsername) {
          ResponseUtils.conflict(res, 'Username already exists');
          return;
        }
      }

      const employee = await User.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).select('-password');

      if (!employee) {
        ResponseUtils.notFound(res, 'Employee not found');
        return;
      }

      ResponseUtils.success(res, employee, 'Employee updated successfully');

    } catch (error) {
      console.error('Update employee error:', error);
      ResponseUtils.error(res, 'Failed to update employee');
    }
  }

  /**
   * Delete employee (soft delete)
   */
  static async deleteEmployee(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const employee = await User.findByIdAndUpdate(
        id,
        { isActive: false, employeeStatus: 'Đã nghỉ việc' },
        { new: true }
      ).select('-password');

      if (!employee) {
        ResponseUtils.notFound(res, 'Employee not found');
        return;
      }

      ResponseUtils.success(res, employee, 'Employee deactivated successfully');

    } catch (error) {
      console.error('Delete employee error:', error);
      ResponseUtils.error(res, 'Failed to deactivate employee');
    }
  }

  /**
   * Reset employee password
   */
  static async resetEmployeePassword(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { newPassword } = req.body;

      const employee = await User.findById(id);

      if (!employee) {
        ResponseUtils.notFound(res, 'Employee not found');
        return;
      }

      // Set new password (will be hashed by pre-save middleware)
      employee.password = newPassword || 'Employee123!';
      await employee.save();

      ResponseUtils.success(res, { message: 'Password reset successfully' }, 'Employee password reset successfully');

    } catch (error) {
      console.error('Reset employee password error:', error);
      ResponseUtils.error(res, 'Failed to reset employee password');
    }
  }

  /**
   * Get employee statistics
   */
  static async getEmployeeStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const stats = await User.aggregate([
        {
          $group: {
            _id: null,
            totalEmployees: { $sum: 1 },
            activeEmployees: {
              $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
            },
            inactiveEmployees: {
              $sum: { $cond: [{ $eq: ['$isActive', false] }, 1, 0] }
            }
          }
        }
      ]);

      const departmentStats = await User.aggregate([
        { $match: { isActive: true } },
        {
          $group: {
            _id: '$department',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]);

      const genderStats = await User.aggregate([
        { $match: { isActive: true } },
        {
          $group: {
            _id: '$gender',
            count: { $sum: 1 }
          }
        }
      ]);

      const statusStats = await User.aggregate([
        {
          $group: {
            _id: '$employeeStatus',
            count: { $sum: 1 }
          }
        }
      ]);

      const result = {
        overview: stats[0] || { totalEmployees: 0, activeEmployees: 0, inactiveEmployees: 0 },
        byDepartment: departmentStats,
        byGender: genderStats,
        byStatus: statusStats
      };

      ResponseUtils.success(res, result, 'Employee statistics retrieved successfully');

    } catch (error) {
      console.error('Get employee stats error:', error);
      ResponseUtils.error(res, 'Failed to retrieve employee statistics');
    }
  }
}

export default EmployeeController;
